import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { ProductTypeEnum } from '@modules/business/enums';
import { BaseAdvancedProductResponseDto } from '../base/base-product-response.dto';

/**
 * Response DTO cho service package
 */
export class ServicePackageResponseDto {
  @ApiProperty({
    description: 'ID service package',
    example: 1,
  })
  @Expose()
  id: number;

  @ApiProperty({
    description: 'Tên gói dịch vụ',
    example: 'Gói tư vấn cơ bản',
  })
  @Expose()
  name: string;

  @ApiProperty({
    description: 'Giá gói dịch vụ',
    example: 1000000,
  })
  @Expose()
  price: number;

  @ApiProperty({
    description: 'Thời gian bắt đầu cung cấp dịch vụ (timestamp)',
    example: 1704067200000,
  })
  @Expose()
  startTime: number;

  @ApiProperty({
    description: 'Thời gian kết thúc cung cấp dịch vụ (timestamp)',
    example: 1704153600000,
  })
  @Expose()
  endTime: number;

  @ApiProperty({
    description: 'Múi giờ',
    example: 'Asia/Ho_Chi_Minh',
  })
  @Expose()
  timezone: string;

  @ApiProperty({
    description: 'Mô tả gói dịch vụ',
    example: 'Gói tư vấn cơ bản bao gồm 3 buổi tư vấn online',
    required: false,
  })
  @Expose()
  description?: string;

  @ApiProperty({
    description: 'Số lượng gói dịch vụ',
    example: 50,
  })
  @Expose()
  quantity: number;

  @ApiProperty({
    description: 'Số lượng tối thiểu mỗi lần mua',
    example: 1,
  })
  @Expose()
  minQuantityPerPurchase: number;

  @ApiProperty({
    description: 'Số lượng tối đa mỗi lần mua',
    example: 3,
  })
  @Expose()
  maxQuantityPerPurchase: number;

  @ApiProperty({
    description: 'Trạng thái service package',
    example: 'PENDING',
  })
  @Expose()
  status: string;

  @ApiProperty({
    description: 'Hình ảnh service package',
    type: [Object],
    required: false,
  })
  @Expose()
  images?: any[];

  @ApiProperty({
    description: 'Thời lượng dịch vụ (giờ)',
    example: 2,
    required: false,
  })
  @Expose()
  durationHours?: number;

  @ApiProperty({
    description: 'Danh sách tính năng',
    type: [String],
    example: ['Phân tích hiện trạng', 'Đưa ra giải pháp', 'Hỗ trợ triển khai'],
    required: false,
  })
  @Expose()
  features?: string[];

  @ApiProperty({
    description: 'Có phải gói phổ biến không',
    example: false,
    required: false,
  })
  @Expose()
  isPopular?: boolean;
}

/**
 * Response DTO cho thông tin nâng cao của dịch vụ
 */
export class ServiceAdvancedInfoResponseDto {
  @ApiProperty({
    description: 'Số lượt mua',
    example: 25,
  })
  @Expose()
  purchaseCount: number;

  @ApiProperty({
    description: 'Danh sách gói dịch vụ',
    type: [ServicePackageResponseDto],
  })
  @Expose()
  @Type(() => ServicePackageResponseDto)
  servicePackages: ServicePackageResponseDto[];

  @ApiProperty({
    description: 'Hình ảnh nâng cao',
    type: [Object],
    required: false,
  })
  @Expose()
  images?: any[];
}

/**
 * Response DTO cho sản phẩm dịch vụ (SERVICE)
 * Kế thừa từ BaseAdvancedProductResponseDto và thêm các trường đặc thù cho dịch vụ
 */
export class ServiceProductResponseDto extends BaseAdvancedProductResponseDto {
  @ApiProperty({
    description: 'Loại sản phẩm - luôn là SERVICE',
    enum: ProductTypeEnum,
    example: ProductTypeEnum.SERVICE,
  })
  @Expose()
  declare productType: ProductTypeEnum.SERVICE;

  @ApiProperty({
    description: 'Thông tin nâng cao của sản phẩm dịch vụ',
    type: ServiceAdvancedInfoResponseDto,
    required: false,
  })
  @Expose()
  @Type(() => ServiceAdvancedInfoResponseDto)
  declare advancedInfo?: ServiceAdvancedInfoResponseDto;

  @ApiProperty({
    description: 'Cấu hình vận chuyển (luôn là 0 cho sản phẩm dịch vụ)',
    example: {
      widthCm: 0,
      heightCm: 0,
      lengthCm: 0,
      weightGram: 0
    },
  })
  @Expose()
  declare shipmentConfig: {
    widthCm: 0;
    heightCm: 0;
    lengthCm: 0;
    weightGram: 0;
  };

  // Service-specific fields for compatibility
  @ApiProperty({
    description: 'Thời gian thực hiện dịch vụ (timestamp)',
    example: 1704067200000,
    required: false,
  })
  @Expose()
  serviceTime?: number;

  @ApiProperty({
    description: 'Thời lượng dịch vụ (phút)',
    example: '60',
    required: false,
  })
  @Expose()
  serviceDuration?: string;

  @ApiProperty({
    description: 'Nhà cung cấp dịch vụ',
    example: 'Công ty tư vấn ABC',
    required: false,
  })
  @Expose()
  serviceProvider?: string;

  @ApiProperty({
    description: 'Loại dịch vụ',
    example: 'CONSULTATION',
    required: false,
  })
  @Expose()
  serviceType?: string;

  @ApiProperty({
    description: 'Địa điểm thực hiện dịch vụ',
    example: 'AT_CENTER',
    required: false,
  })
  @Expose()
  serviceLocation?: string;
}
