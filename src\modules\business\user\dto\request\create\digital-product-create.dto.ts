import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsNotEmpty,
  IsObject,
  ValidateNested,
  IsNumber,
  IsArray,
  IsString,
  IsEnum,
  Min,
} from 'class-validator';
import { BaseProductDto } from '../base-product/base-product.dto';
import { HasPriceDto } from '../../price.dto';
import { DigitalClassificationDto } from '../../digital-classification.dto';

// Types cho Digital Product
export type DeliveryMethodType = 'DASHBOARD' | 'EMAIL' | 'SMS' | 'DIRECT_MESSAGE' | 'ZALO' | 'AUTO_ACTIVE';
export type DeliveryTimingType = 'IMMEDIATE' | 'DELAYED';
export type OutputType = 'DOWNLOAD_LINK' | 'ACCESS_CODE' | 'ACCOUNT_INFO' | 'CONTENT';

/**
 * DTO cho Digital Fulfillment Flow
 */
export class DigitalFulfillmentFlowDto {
  @ApiProperty({
    description: 'Phương thức giao hàng',
    example: 'DASHBOARD',
    enum: ['DASHBOARD', 'EMAIL', 'SMS', 'DIRECT_MESSAGE', 'ZALO', 'AUTO_ACTIVE'],
  })
  @IsString()
  @IsNotEmpty()
  @IsEnum(['DASHBOARD', 'EMAIL', 'SMS', 'DIRECT_MESSAGE', 'ZALO', 'AUTO_ACTIVE'])
  deliveryMethod: DeliveryMethodType;

  @ApiProperty({
    description: 'Thời điểm giao hàng',
    example: 'IMMEDIATE',
    enum: ['IMMEDIATE', 'DELAYED'],
  })
  @IsString()
  @IsNotEmpty()
  @IsEnum(['IMMEDIATE', 'DELAYED'])
  deliveryTiming: DeliveryTimingType;
}

/**
 * DTO cho Digital Output
 */
export class DigitalOutputDto {
  @ApiProperty({
    description: 'Loại output',
    example: 'DOWNLOAD_LINK',
    enum: ['DOWNLOAD_LINK', 'ACCESS_CODE', 'ACCOUNT_INFO', 'CONTENT'],
  })
  @IsString()
  @IsNotEmpty()
  @IsEnum(['DOWNLOAD_LINK', 'ACCESS_CODE', 'ACCOUNT_INFO', 'CONTENT'])
  outputType: OutputType;

  @ApiProperty({
    description: 'Link truy cập',
    example: 'https://course.example.com/activate?token=abc123',
  })
  @IsString()
  @IsNotEmpty()
  accessLink: string;

  @ApiProperty({
    description: 'Hướng dẫn sử dụng',
    example: 'Vui lòng đăng nhập bằng thông tin được cung cấp để truy cập khóa học',
  })
  @IsString()
  @IsNotEmpty()
  usageInstructions: string;
}

/**
 * DTO cho việc tạo sản phẩm số (DIGITAL)
 * Kế thừa từ BaseProductDto và thêm các trường đặc thù cho sản phẩm số
 */
export class DigitalProductCreateDto extends BaseProductDto {
  @ApiProperty({
    description: 'Số lượt mua',
    example: 0,
  })
  @IsNumber()
  @Min(0)
  purchaseCount: number;

  @ApiProperty({
    description: 'Digital fulfillment flow',
    type: DigitalFulfillmentFlowDto,
  })
  @IsObject()
  @ValidateNested()
  @Type(() => DigitalFulfillmentFlowDto)
  digitalFulfillmentFlow: DigitalFulfillmentFlowDto;

  @ApiProperty({
    description: 'Digital output',
    type: DigitalOutputDto,
  })
  @IsObject()
  @ValidateNested()
  @Type(() => DigitalOutputDto)
  digitalOutput: DigitalOutputDto;

  @ApiProperty({
    description: 'Danh sách phân loại sản phẩm số (chuyển từ variants)',
    type: [DigitalClassificationDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => DigitalClassificationDto)
  classifications: DigitalClassificationDto[];

  // Override price để bắt buộc cho DIGITAL
  @ApiProperty({
    description: 'Thông tin giá sản phẩm - Bắt buộc đối với sản phẩm số',
    oneOf: [
      { $ref: '#/components/schemas/HasPriceDto' },
      { $ref: '#/components/schemas/StringPriceDto' },
      { $ref: '#/components/schemas/ClassificationPriceDto' },
      { $ref: '#/components/schemas/ClassificationStringPriceDto' }
    ],
  })
  @IsNotEmpty()
  @IsObject()
  declare price: HasPriceDto; // Required for digital products
}
