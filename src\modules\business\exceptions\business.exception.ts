import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '@common/exceptions';

/**
 * Mã lỗi cho module Business
 */
export const BUSINESS_ERROR_CODES = {
  INVALID_INPUT: new ErrorCode(
    30000,
    'Lỗi khóa nhập dữ liệu',
    HttpStatus.BAD_REQUEST,
  ),

  WAREHOUSE_VALIDATION_FAILED: new ErrorCode(
    30000,
    'Lỗi kiểm tra dữ liệu kho',
    HttpStatus.BAD_REQUEST,
  ),

  WAREHOUSE_FETCH_FAILED: new ErrorCode(
    30001,
    'Lỗi khi lấy thông tin kho',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  WAREHOUSE_DELETE_FAILED: new ErrorCode(
    30002,
    'Lỗi khi xóa kho',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // Inventory exceptions (30201-30220)
  INVENTORY_NOT_FOUND: new ErrorCode(
    30201,
    'Không tìm thấy tồn kho',
    HttpStatus.NOT_FOUND,
  ),
  INVENTORY_CREATION_FAILED: new ErrorCode(
    30202,
    'Lỗi khi tạo tồn kho',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  INVENTORY_UPDATE_FAILED: new ErrorCode(
    30203,
    'Lỗi khi cập nhật tồn kho',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  INVENTORY_DELETE_FAILED: new ErrorCode(
    30204,
    'Lỗi khi xóa tồn kho',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  INVENTORY_FETCH_FAILED: new ErrorCode(
    30205,
    'Lỗi khi lấy thông tin tồn kho',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  INVENTORY_VALIDATION_FAILED: new ErrorCode(
    30206,
    'Lỗi kiểm tra dữ liệu tồn kho',
    HttpStatus.BAD_REQUEST,
  ),
  INVENTORY_ACCESS_DENIED: new ErrorCode(
    30207,
    'Bạn không có quyền truy cập tồn kho này',
    HttpStatus.FORBIDDEN,
  ),
  INVENTORY_ALREADY_USED: new ErrorCode(
    30208,
    'Inventory đã được sử dụng cho sản phẩm khác',
    HttpStatus.CONFLICT,
  ),

  // Product exceptions (30001-30020)
  CLASSIFICATION_VALIDATION_FAILED: new ErrorCode(
    30001,
    'Classification validation failed',
    HttpStatus.BAD_REQUEST,
  ),

  INTEGRATION_CREATION_FAILED: new ErrorCode(
    30000,
    'Failed to create integration',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  INTEGRATION_UPDATE_FAILED: new ErrorCode(
    30000,
    'Failed to update integration',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  INTEGRATION_DELETION_FAILED: new ErrorCode(
    30000,
    'Failed to delete integration',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  INTEGRATION_FIND_FAILED: new ErrorCode(
    30000,
    'Failed to find integrations',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  CUSTOM_FIELD_DELETE_FAILED: new ErrorCode(
    30000,
    'Failed to delete custom field',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  CUSTOM_FIELD_CONFIG_ID_EXISTS: new ErrorCode(
    30105,
    'ConfigId đã tồn tại',
    HttpStatus.CONFLICT,
  ),

  PRODUCT_MAPPING_FAILED: new ErrorCode(
    30000,
    'Failed to map product to DTO',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  PRODUCT_NOT_FOUND: new ErrorCode(
    30001,
    'Product not found',
    HttpStatus.NOT_FOUND,
  ),
  PRODUCT_CREATION_FAILED: new ErrorCode(
    30002,
    'Failed to create product',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  INVALID_PRODUCT_TYPE: new ErrorCode(
    30007,
    'Invalid product type',
    HttpStatus.BAD_REQUEST,
  ),
  PRODUCT_UPDATE_FAILED: new ErrorCode(
    30003,
    'Failed to update product',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  PRODUCT_DELETION_FAILED: new ErrorCode(
    30004,
    'Failed to delete product',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  PRODUCT_FIND_FAILED: new ErrorCode(
    30005,
    'Failed to find products',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  PRODUCT_UNAUTHORIZED: new ErrorCode(
    30006,
    'Unauthorized to access this product',
    HttpStatus.FORBIDDEN,
  ),

  // Custom Field exceptions (30101-30120)
  CUSTOM_FIELD_NOT_FOUND: new ErrorCode(
    30101,
    'Custom field not found',
    HttpStatus.NOT_FOUND,
  ),
  CUSTOM_FIELD_CREATION_FAILED: new ErrorCode(
    30102,
    'Failed to create custom field',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  CUSTOM_FIELD_UPDATE_FAILED: new ErrorCode(
    30103,
    'Failed to update custom field',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  CUSTOM_FIELD_DELETION_FAILED: new ErrorCode(
    30104,
    'Failed to delete custom field',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  CUSTOM_FIELD_FIND_FAILED: new ErrorCode(
    30105,
    'Failed to find custom fields',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  CUSTOM_FIELD_REQUIRED: new ErrorCode(
    30106,
    'Custom field is required',
    HttpStatus.BAD_REQUEST,
  ),
  CUSTOM_FIELD_VALIDATION_FAILED: new ErrorCode(
    30107,
    'Custom field validation failed',
    HttpStatus.BAD_REQUEST,
  ),
  CUSTOM_FIELD_NOT_IN_GROUP: new ErrorCode(
    30108,
    'Custom field not in group',
    HttpStatus.BAD_REQUEST,
  ),

  // Group Form exceptions (30121-30140)
  GROUP_FORM_NOT_FOUND: new ErrorCode(
    30121,
    'Group form not found',
    HttpStatus.NOT_FOUND,
  ),
  GROUP_FORM_CREATION_FAILED: new ErrorCode(
    30122,
    'Failed to create group form',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  GROUP_FORM_UPDATE_FAILED: new ErrorCode(
    30123,
    'Failed to update group form',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  GROUP_FORM_DELETION_FAILED: new ErrorCode(
    30124,
    'Failed to delete group form',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  GROUP_FORM_FIND_FAILED: new ErrorCode(
    30125,
    'Failed to find group forms',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  GROUP_FORM_INVALID_SORT_FIELD: new ErrorCode(
    30126,
    'Invalid sort field for group form',
    HttpStatus.BAD_REQUEST,
  ),

  // Classification exceptions (30141-30160)
  CLASSIFICATION_NOT_FOUND: new ErrorCode(
    30141,
    'Classification not found',
    HttpStatus.NOT_FOUND,
  ),
  CLASSIFICATION_CREATION_FAILED: new ErrorCode(
    30142,
    'Failed to create classification',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  CLASSIFICATION_UPDATE_FAILED: new ErrorCode(
    30143,
    'Failed to update classification',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  CLASSIFICATION_DELETION_FAILED: new ErrorCode(
    30144,
    'Failed to delete classification',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  CLASSIFICATION_FIND_FAILED: new ErrorCode(
    30145,
    'Failed to find classifications',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // Order exceptions (30021-30040)
  ORDER_NOT_FOUND: new ErrorCode(
    30021,
    'Order not found',
    HttpStatus.NOT_FOUND,
  ),
  ORDER_CREATION_FAILED: new ErrorCode(
    30022,
    'Failed to create order',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  ORDER_CREATE_FAILED: new ErrorCode(
    30022,
    'Lỗi khi tạo đơn hàng',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  ORDER_UPDATE_FAILED: new ErrorCode(
    30023,
    'Failed to update order',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  ORDER_DELETION_FAILED: new ErrorCode(
    30024,
    'Failed to delete order',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  ORDER_FIND_FAILED: new ErrorCode(
    30025,
    'Failed to find orders',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  ORDER_UNAUTHORIZED: new ErrorCode(
    30026,
    'Unauthorized to access this order',
    HttpStatus.FORBIDDEN,
  ),

  // Warehouse exceptions (30041-30060)
  WAREHOUSE_NOT_FOUND: new ErrorCode(
    30041,
    'Warehouse not found',
    HttpStatus.NOT_FOUND,
  ),
  WAREHOUSE_CREATION_FAILED: new ErrorCode(
    30042,
    'Failed to create warehouse',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  WAREHOUSE_UPDATE_FAILED: new ErrorCode(
    30043,
    'Failed to update warehouse',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  WAREHOUSE_DELETION_FAILED: new ErrorCode(
    30044,
    'Failed to delete warehouse',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  WAREHOUSE_FIND_FAILED: new ErrorCode(
    30045,
    'Failed to find warehouses',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  WAREHOUSE_NAME_ALREADY_EXISTS: new ErrorCode(
    30046,
    'Warehouse name already exists',
    HttpStatus.CONFLICT,
  ),
  WAREHOUSE_VALIDATION_ERROR: new ErrorCode(
    30047,
    'Warehouse validation error',
    HttpStatus.BAD_REQUEST,
  ),
  WAREHOUSE_ACCESS_DENIED: new ErrorCode(
    30048,
    'Bạn không có quyền truy cập kho này',
    HttpStatus.FORBIDDEN,
  ),
  WAREHOUSE_CUSTOM_FIELD_NOT_FOUND: new ErrorCode(
    30048,
    'Warehouse custom field not found',
    HttpStatus.NOT_FOUND,
  ),
  WAREHOUSE_CUSTOM_FIELD_ALREADY_EXISTS: new ErrorCode(
    30049,
    'Warehouse custom field already exists',
    HttpStatus.CONFLICT,
  ),
  WAREHOUSE_CUSTOM_FIELD_CREATION_FAILED: new ErrorCode(
    30050,
    'Failed to create warehouse custom field',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  WAREHOUSE_CUSTOM_FIELD_UPDATE_FAILED: new ErrorCode(
    30051,
    'Failed to update warehouse custom field',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  WAREHOUSE_CUSTOM_FIELD_DELETION_FAILED: new ErrorCode(
    30052,
    'Failed to delete warehouse custom field',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  WAREHOUSE_CUSTOM_FIELD_FIND_FAILED: new ErrorCode(
    30053,
    'Failed to find warehouse custom fields',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  WAREHOUSE_CUSTOM_FIELD_VALIDATION_ERROR: new ErrorCode(
    30054,
    'Warehouse custom field validation error',
    HttpStatus.BAD_REQUEST,
  ),

  // Physical Warehouse exceptions (30221-30240)
  WAREHOUSE_TYPE_MISMATCH: new ErrorCode(
    30221,
    'Loại kho không phù hợp',
    HttpStatus.BAD_REQUEST,
  ),
  WAREHOUSE_ALREADY_EXISTS: new ErrorCode(
    30222,
    'Kho vật lý đã tồn tại',
    HttpStatus.CONFLICT,
  ),

  // File exceptions (30241-30260)
  FILE_NOT_FOUND: new ErrorCode(
    30241,
    'Không tìm thấy file',
    HttpStatus.NOT_FOUND,
  ),
  FILE_CREATION_FAILED: new ErrorCode(
    30242,
    'Lỗi khi tạo file',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  FILE_UPDATE_FAILED: new ErrorCode(
    30243,
    'Lỗi khi cập nhật file',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  FILE_DELETE_FAILED: new ErrorCode(
    30244,
    'Lỗi khi xóa file',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  FILE_FETCH_FAILED: new ErrorCode(
    30245,
    'Lỗi khi lấy thông tin file',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  FILE_ACCESS_DENIED: new ErrorCode(
    30246,
    'Bạn không có quyền truy cập file này',
    HttpStatus.FORBIDDEN,
  ),

  // Folder exceptions (30261-30280)
  FOLDER_NOT_FOUND: new ErrorCode(
    30261,
    'Không tìm thấy thư mục',
    HttpStatus.NOT_FOUND,
  ),

  // Report exceptions (30401-30420)
  REPORT_OVERVIEW_FAILED: new ErrorCode(
    30401,
    'Lỗi khi lấy dữ liệu tổng quan báo cáo',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  REPORT_SALES_CHART_FAILED: new ErrorCode(
    30402,
    'Lỗi khi lấy dữ liệu biểu đồ doanh thu',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  REPORT_ORDERS_CHART_FAILED: new ErrorCode(
    30403,
    'Lỗi khi lấy dữ liệu biểu đồ đơn hàng',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  REPORT_CUSTOMERS_CHART_FAILED: new ErrorCode(
    30404,
    'Lỗi khi lấy dữ liệu biểu đồ khách hàng',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  REPORT_PRODUCTS_CHART_FAILED: new ErrorCode(
    30405,
    'Lỗi khi lấy dữ liệu biểu đồ sản phẩm',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  REPORT_TOP_SELLING_PRODUCTS_FAILED: new ErrorCode(
    30406,
    'Lỗi khi lấy danh sách sản phẩm bán chạy',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  REPORT_POTENTIAL_CUSTOMERS_FAILED: new ErrorCode(
    30407,
    'Lỗi khi lấy danh sách khách hàng tiềm năng',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  REPORT_DATE_RANGE_INVALID: new ErrorCode(
    30408,
    'Khoảng thời gian báo cáo không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),
  FOLDER_CREATION_FAILED: new ErrorCode(
    30262,
    'Lỗi khi tạo thư mục',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  FOLDER_UPDATE_FAILED: new ErrorCode(
    30263,
    'Lỗi khi cập nhật thư mục',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  FOLDER_DELETE_FAILED: new ErrorCode(
    30264,
    'Lỗi khi xóa thư mục',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  FOLDER_FETCH_FAILED: new ErrorCode(
    30265,
    'Lỗi khi lấy thông tin thư mục',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // Supplier exceptions (30061-30080)
  SUPPLIER_NOT_FOUND: new ErrorCode(
    30061,
    'Supplier not found',
    HttpStatus.NOT_FOUND,
  ),
  SUPPLIER_CREATION_FAILED: new ErrorCode(
    30062,
    'Failed to create supplier',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  SUPPLIER_UPDATE_FAILED: new ErrorCode(
    30063,
    'Failed to update supplier',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  SUPPLIER_DELETION_FAILED: new ErrorCode(
    30064,
    'Failed to delete supplier',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  SUPPLIER_FIND_FAILED: new ErrorCode(
    30065,
    'Failed to find suppliers',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // User Convert exceptions (30161-30180)
  CONVERT_NOT_FOUND: new ErrorCode(
    30161,
    'Convert record not found',
    HttpStatus.NOT_FOUND,
  ),
  CONVERT_CREATION_FAILED: new ErrorCode(
    30162,
    'Failed to create convert record',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  CONVERT_UPDATE_FAILED: new ErrorCode(
    30163,
    'Failed to update convert record',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  CONVERT_DELETION_FAILED: new ErrorCode(
    30164,
    'Failed to delete convert record',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  CONVERT_FIND_FAILED: new ErrorCode(
    30165,
    'Failed to find convert records',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  CONVERT_ACCESS_DENIED: new ErrorCode(
    30166,
    'Access denied to this convert record',
    HttpStatus.FORBIDDEN,
  ),

  // User Convert Customer exceptions (30181-30200)
  CONVERT_CUSTOMER_NOT_FOUND: new ErrorCode(
    30181,
    'Convert customer not found',
    HttpStatus.NOT_FOUND,
  ),
  CUSTOMER_NOT_FOUND: new ErrorCode(
    30181,
    'Khách hàng không tồn tại',
    HttpStatus.NOT_FOUND,
  ),
  ADDRESS_NOT_FOUND: new ErrorCode(
    30182,
    'Địa chỉ không tồn tại',
    HttpStatus.NOT_FOUND,
  ),
  CONVERT_CUSTOMER_CREATION_FAILED: new ErrorCode(
    30182,
    'Failed to create convert customer',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  CONVERT_CUSTOMER_UPDATE_FAILED: new ErrorCode(
    30183,
    'Failed to update convert customer',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  CONVERT_CUSTOMER_DELETION_FAILED: new ErrorCode(
    30184,
    'Failed to delete convert customer',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  USER_CONVERT_CUSTOMER_DELETE_FAILED: new ErrorCode(
    30194,
    'Failed to delete user convert customer',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  CONVERT_CUSTOMER_FIND_FAILED: new ErrorCode(
    30185,
    'Failed to find convert customers',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  CONVERT_CUSTOMER_ACCESS_DENIED: new ErrorCode(
    30186,
    'Access denied to this convert customer',
    HttpStatus.FORBIDDEN,
  ),
  CONVERT_CUSTOMER_PHONE_DUPLICATE: new ErrorCode(
    30187,
    'Phone number already exists',
    HttpStatus.CONFLICT,
  ),
  CONVERT_CUSTOMER_MERGE_FAILED: new ErrorCode(
    30188,
    'Failed to merge convert customers',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  CONVERT_CUSTOMER_MERGE_SAME_CUSTOMER: new ErrorCode(
    30189,
    'Cannot merge customer with itself',
    HttpStatus.BAD_REQUEST,
  ),
  CONVERT_CUSTOMER_MERGE_DIFFERENT_USER: new ErrorCode(
    30190,
    'Cannot merge customers from different users',
    HttpStatus.FORBIDDEN,
  ),

  // User Convert Customer Bulk Operations exceptions (30191-30195)
  CONVERT_CUSTOMER_BULK_CREATION_FAILED: new ErrorCode(
    30191,
    'Failed to bulk create convert customers',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  CONVERT_CUSTOMER_BULK_VALIDATION_FAILED: new ErrorCode(
    30192,
    'Bulk create validation failed',
    HttpStatus.BAD_REQUEST,
  ),
  CONVERT_CUSTOMER_BULK_PARTIAL_SUCCESS: new ErrorCode(
    30193,
    'Bulk create completed with some failures',
    HttpStatus.PARTIAL_CONTENT,
  ),

  // User Convert Customer Custom Fields exceptions (30196-30200)
  CONVERT_CUSTOMER_CUSTOM_FIELD_NOT_FOUND: new ErrorCode(
    30196,
    'Convert customer custom field not found',
    HttpStatus.NOT_FOUND,
  ),
  CONVERT_CUSTOMER_CUSTOM_FIELD_CREATION_FAILED: new ErrorCode(
    30197,
    'Failed to create convert customer custom field',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  CONVERT_CUSTOMER_CUSTOM_FIELD_UPDATE_FAILED: new ErrorCode(
    30198,
    'Failed to update convert customer custom field',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  CONVERT_CUSTOMER_CUSTOM_FIELD_DELETION_FAILED: new ErrorCode(
    30199,
    'Failed to delete convert customer custom field',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  CONVERT_CUSTOMER_CUSTOM_FIELD_FIND_FAILED: new ErrorCode(
    30200,
    'Failed to find convert customer custom fields',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  CONVERT_CUSTOMER_CUSTOM_FIELD_CONFIG_ID_DUPLICATE: new ErrorCode(
    30196,
    'Custom field config ID already exists for this user',
    HttpStatus.CONFLICT,
  ),
  CONVERT_CUSTOMER_CUSTOM_FIELD_VALIDATION_FAILED: new ErrorCode(
    30197,
    'Custom field validation failed',
    HttpStatus.BAD_REQUEST,
  ),

  // Customer Social exceptions (30198-30210)
  CUSTOMER_SOCIAL_NOT_FOUND: new ErrorCode(
    30198,
    'Customer social data not found',
    HttpStatus.NOT_FOUND,
  ),
  CUSTOMER_SOCIAL_UPDATE_FAILED: new ErrorCode(
    30199,
    'Failed to update customer social data',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  CUSTOMER_SOCIAL_VALIDATION_FAILED: new ErrorCode(
    30200,
    'Customer social data validation failed',
    HttpStatus.BAD_REQUEST,
  ),
  CUSTOMER_FACEBOOK_PAGE_SCOPED_ID_DUPLICATE: new ErrorCode(
    30201,
    'Facebook page scoped ID already exists',
    HttpStatus.CONFLICT,
  ),

  // Order specific exceptions
  ORDER_ACCESS_DENIED: new ErrorCode(
    30027,
    'Access denied to this order',
    HttpStatus.FORBIDDEN,
  ),
  ORDER_PRINT_FAILED: new ErrorCode(
    30028,
    'Failed to print order',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  ORDER_NO_SHIPPING_INFO: new ErrorCode(
    30029,
    'Order has no shipping information for printing',
    HttpStatus.BAD_REQUEST,
  ),

  // Order bulk operations exceptions (30040-30049)
  ORDER_BULK_DELETE_FAILED: new ErrorCode(
    30040,
    'Lỗi khi xóa nhiều đơn hàng',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  ORDER_BULK_DELETE_PARTIAL_SUCCESS: new ErrorCode(
    30041,
    'Xóa nhiều đơn hàng hoàn thành với một số lỗi',
    HttpStatus.PARTIAL_CONTENT,
  ),
  ORDER_BULK_DELETE_VALIDATION_FAILED: new ErrorCode(
    30042,
    'Validation xóa nhiều đơn hàng thất bại',
    HttpStatus.BAD_REQUEST,
  ),

  // Payment validation exceptions (30030-30039)
  PAYMENT_VALIDATION_FAILED: new ErrorCode(
    30030,
    'Payment validation failed',
    HttpStatus.BAD_REQUEST,
  ),
  COD_LIMIT_EXCEEDED: new ErrorCode(
    30031,
    'COD amount exceeds limit',
    HttpStatus.BAD_REQUEST,
  ),
  CREDIT_LIMIT_EXCEEDED: new ErrorCode(
    30032,
    'Credit limit exceeded',
    HttpStatus.BAD_REQUEST,
  ),
  INVALID_PAYMENT_STATUS: new ErrorCode(
    30033,
    'Invalid payment status transition',
    HttpStatus.BAD_REQUEST,
  ),

  // Address validation exceptions (30034-30039)
  ADDRESS_VALIDATION_FAILED: new ErrorCode(
    30034,
    'Address validation failed',
    HttpStatus.BAD_REQUEST,
  ),

  // GHTK API exceptions (30501-30520)
  GHTK_API_ERROR: new ErrorCode(
    30501,
    'Lỗi khi gọi API GHTK',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  GHTK_INVALID_TOKEN: new ErrorCode(
    30502,
    'Token GHTK không hợp lệ',
    HttpStatus.UNAUTHORIZED,
  ),
  GHTK_INVALID_PARTNER_CODE: new ErrorCode(
    30503,
    'Mã đối tác GHTK không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),
  GHTK_ORDER_NOT_FOUND: new ErrorCode(
    30504,
    'Không tìm thấy đơn hàng GHTK',
    HttpStatus.NOT_FOUND,
  ),
  GHTK_CANNOT_CANCEL: new ErrorCode(
    30505,
    'Không thể hủy đơn hàng GHTK',
    HttpStatus.BAD_REQUEST,
  ),
  GHTK_INVALID_ADDRESS: new ErrorCode(
    30506,
    'Địa chỉ GHTK không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),
  GHTK_INVALID_WEIGHT: new ErrorCode(
    30507,
    'Trọng lượng GHTK không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),
  GHTK_INVALID_VALUE: new ErrorCode(
    30508,
    'Giá trị đơn hàng GHTK không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),
  GHTK_NETWORK_ERROR: new ErrorCode(
    30509,
    'Lỗi kết nối mạng với GHTK',
    HttpStatus.SERVICE_UNAVAILABLE,
  ),
  GHTK_TIMEOUT_ERROR: new ErrorCode(
    30510,
    'Timeout khi gọi API GHTK',
    HttpStatus.REQUEST_TIMEOUT,
  ),
  GHTK_CONFIG_ERROR: new ErrorCode(
    30511,
    'Lỗi cấu hình GHTK',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  GHTK_WEBHOOK_ERROR: new ErrorCode(
    30512,
    'Lỗi xử lý webhook GHTK',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // GHN API exceptions (30521-30540)
  GHN_API_ERROR: new ErrorCode(
    30521,
    'Lỗi khi gọi API GHN',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  GHN_INVALID_TOKEN: new ErrorCode(
    30522,
    'Token GHN không hợp lệ',
    HttpStatus.UNAUTHORIZED,
  ),
  GHN_INVALID_SHOP_ID: new ErrorCode(
    30523,
    'Shop ID GHN không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),
  GHN_ORDER_NOT_FOUND: new ErrorCode(
    30524,
    'Không tìm thấy đơn hàng GHN',
    HttpStatus.NOT_FOUND,
  ),
  GHN_CANNOT_CANCEL: new ErrorCode(
    30525,
    'Không thể hủy đơn hàng GHN',
    HttpStatus.BAD_REQUEST,
  ),
  GHN_INVALID_ADDRESS: new ErrorCode(
    30526,
    'Địa chỉ GHN không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),
  GHN_INVALID_WEIGHT: new ErrorCode(
    30527,
    'Trọng lượng GHN không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),
  GHN_INVALID_VALUE: new ErrorCode(
    30528,
    'Giá trị đơn hàng GHN không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),
  GHN_NETWORK_ERROR: new ErrorCode(
    30529,
    'Lỗi kết nối mạng với GHN',
    HttpStatus.SERVICE_UNAVAILABLE,
  ),
  GHN_TIMEOUT_ERROR: new ErrorCode(
    30530,
    'Timeout khi gọi API GHN',
    HttpStatus.REQUEST_TIMEOUT,
  ),
  GHN_INVALID_CONFIG: new ErrorCode(
    30531,
    'Cấu hình GHN không hợp lệ',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  GHTK_INVALID_CONFIG: new ErrorCode(
    30538,
    'Cấu hình GHTK không hợp lệ',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  GHN_WEBHOOK_ERROR: new ErrorCode(
    30532,
    'Lỗi xử lý webhook GHN',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  GHN_INVALID_SERVICE: new ErrorCode(
    30533,
    'Dịch vụ GHN không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),
  GHN_WEIGHT_EXCEEDED: new ErrorCode(
    30534,
    'Khối lượng vượt quá giới hạn GHN',
    HttpStatus.BAD_REQUEST,
  ),
  GHN_DIMENSION_EXCEEDED: new ErrorCode(
    30535,
    'Kích thước vượt quá giới hạn GHN',
    HttpStatus.BAD_REQUEST,
  ),
  GHN_COD_EXCEEDED: new ErrorCode(
    30536,
    'Số tiền COD vượt quá giới hạn GHN',
    HttpStatus.BAD_REQUEST,
  ),
  GHN_INSURANCE_EXCEEDED: new ErrorCode(
    30537,
    'Giá trị bảo hiểm vượt quá giới hạn GHN',
    HttpStatus.BAD_REQUEST,
  ),
};
