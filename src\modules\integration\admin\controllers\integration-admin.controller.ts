import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiExtraModels,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { JwtEmployeeGuard } from '@modules/auth/guards';
import { CurrentEmployee } from '@modules/auth/decorators';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { ApiErrorResponseDto } from '@common/dto/api-error-response.dto';
import { SWAGGER_API_TAGS } from '@common/swagger/swagger.tags';
import { INTEGRATION_ERROR_CODES } from '@modules/integration/exceptions/integration-error.code';
import { IntegrationAdminService } from '../services/integration-admin.service';
import {
  CreateIntegrationAdminDto,
  UpdateIntegrationAdminDto,
  IntegrationAdminResponseDto,
  QueryIntegrationAdminDto,
} from '../dto/integration';

/**
 * Controller xử lý các API liên quan đến tích hợp cho admin
 */
@ApiTags(SWAGGER_API_TAGS.INTEGRATION_ADMIN)
@ApiExtraModels(ApiResponseDto, IntegrationAdminResponseDto, PaginatedResult, ApiErrorResponseDto)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtEmployeeGuard)
@Controller('admin/integration')
export class IntegrationAdminController {
  constructor(private readonly integrationAdminService: IntegrationAdminService) {}

  /**
   * Lấy danh sách tích hợp cho admin
   * @param queryDto Tham số truy vấn
   * @param employee Thông tin admin
   * @returns Danh sách tích hợp với phân trang
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách tích hợp cho admin' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách tích hợp',
    schema: ApiResponseDto.getPaginatedSchema(IntegrationAdminResponseDto),
  })
  @ApiErrorResponse(INTEGRATION_ERROR_CODES.INTEGRATION_LIST_FAILED)
  async getIntegrations(
    @Query() queryDto: QueryIntegrationAdminDto,
    @CurrentEmployee() employee: JwtPayload,
  ): Promise<ApiResponseDto<PaginatedResult<IntegrationAdminResponseDto>>> {
    const result = await this.integrationAdminService.getIntegrations(queryDto, employee.id);
    return ApiResponseDto.success(result, 'Lấy danh sách tích hợp thành công');
  }

  /**
   * Lấy thông tin tích hợp theo ID cho admin
   * @param id ID của tích hợp
   * @param employee Thông tin admin
   * @returns Thông tin chi tiết của tích hợp
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy thông tin tích hợp theo ID cho admin' })
  @ApiParam({ name: 'id', description: 'ID của tích hợp', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Thông tin tích hợp',
    schema: ApiResponseDto.getSchema(IntegrationAdminResponseDto),
  })
  @ApiErrorResponse(INTEGRATION_ERROR_CODES.INTEGRATION_NOT_FOUND, INTEGRATION_ERROR_CODES.INTEGRATION_LIST_FAILED)
  async getIntegrationById(
    @Param('id', ParseIntPipe) id: number,
    @CurrentEmployee() employee: JwtPayload,
  ): Promise<ApiResponseDto<IntegrationAdminResponseDto>> {
    const result = await this.integrationAdminService.getIntegrationById(id, employee.id);
    return ApiResponseDto.success(result, 'Lấy thông tin tích hợp thành công');
  }

  /**
   * Tạo mới tích hợp bởi admin
   * @param createDto DTO chứa thông tin tạo mới
   * @param employee Thông tin admin
   * @returns Thông tin tích hợp đã tạo
   */
  @Post()
  @ApiOperation({ summary: 'Tạo mới tích hợp bởi admin' })
  @ApiBody({ type: CreateIntegrationAdminDto })
  @ApiResponse({
    status: 201,
    description: 'Tạo mới tích hợp thành công',
    schema: ApiResponseDto.getSchema(IntegrationAdminResponseDto),
  })
  @ApiErrorResponse(INTEGRATION_ERROR_CODES.INTEGRATION_CREATE_FAILED)
  async createIntegration(
    @Body() createDto: CreateIntegrationAdminDto,
    @CurrentEmployee() employee: JwtPayload,
  ): Promise<ApiResponseDto<IntegrationAdminResponseDto>> {
    const result = await this.integrationAdminService.createIntegration(createDto, employee.id);
    return ApiResponseDto.created(result, 'Tạo mới tích hợp thành công');
  }

  /**
   * Cập nhật tích hợp bởi admin
   * @param id ID của tích hợp
   * @param updateDto DTO chứa thông tin cập nhật
   * @param employee Thông tin admin
   * @returns Thông tin tích hợp đã cập nhật
   */
  @Put(':id')
  @ApiOperation({ summary: 'Cập nhật tích hợp bởi admin' })
  @ApiParam({ name: 'id', description: 'ID của tích hợp', type: 'number' })
  @ApiBody({ type: UpdateIntegrationAdminDto })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật tích hợp thành công',
    schema: ApiResponseDto.getSchema(IntegrationAdminResponseDto),
  })
  @ApiErrorResponse(INTEGRATION_ERROR_CODES.INTEGRATION_NOT_FOUND, INTEGRATION_ERROR_CODES.INTEGRATION_UPDATE_FAILED)
  async updateIntegration(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateIntegrationAdminDto,
    @CurrentEmployee() employee: JwtPayload,
  ): Promise<ApiResponseDto<IntegrationAdminResponseDto>> {
    const result = await this.integrationAdminService.updateIntegration(id, updateDto, employee.id);
    return ApiResponseDto.success(result, 'Cập nhật tích hợp thành công');
  }

  /**
   * Xóa tích hợp bởi admin
   * @param id ID của tích hợp
   * @param employee Thông tin admin
   * @returns Kết quả xóa
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa tích hợp bởi admin' })
  @ApiParam({ name: 'id', description: 'ID của tích hợp', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Xóa tích hợp thành công',
    schema: ApiResponseDto.getSchema(null),
  })
  @ApiErrorResponse(INTEGRATION_ERROR_CODES.INTEGRATION_NOT_FOUND, INTEGRATION_ERROR_CODES.INTEGRATION_DELETE_FAILED)
  async deleteIntegration(
    @Param('id', ParseIntPipe) id: number,
    @CurrentEmployee() employee: JwtPayload,
  ): Promise<ApiResponseDto<null>> {
    await this.integrationAdminService.deleteIntegration(id, employee.id);
    return ApiResponseDto.success(null, 'Xóa tích hợp thành công');
  }
}
