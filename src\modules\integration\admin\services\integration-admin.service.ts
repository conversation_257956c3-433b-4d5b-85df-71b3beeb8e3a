import { Injectable, Logger } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';
import { plainToInstance } from 'class-transformer';
import { AppException } from '@common/exceptions';
import { IntegrationRepository } from '@modules/integration/repositories';
import { Integration } from '@modules/integration/entities';
import { OwnedTypeEnum } from '@modules/integration/enums';
import { INTEGRATION_ERROR_CODES } from '@modules/integration/exceptions/integration-error.code';
import { PaginatedResult } from '@common/response';
import {
  CreateIntegrationAdminDto,
  UpdateIntegrationAdminDto,
  IntegrationAdminResponseDto,
  QueryIntegrationAdminDto,
} from '../dto/integration';

/**
 * Service xử lý các thao tác liên quan đến tích hợp cho admin
 */
@Injectable()
export class IntegrationAdminService {
  private readonly logger = new Logger(IntegrationAdminService.name);

  constructor(private readonly integrationRepository: IntegrationRepository) {}

  /**
   * Tạo mới tích hợp bởi admin
   * @param createDto DTO chứa thông tin tạo mới
   * @param adminId ID của admin tạo
   * @returns Thông tin tích hợp đã tạo
   */
  @Transactional()
  async createIntegration(
    createDto: CreateIntegrationAdminDto,
    adminId: number,
  ): Promise<IntegrationAdminResponseDto> {
    try {
      // Tạo đối tượng Integration mới
      const integration = new Integration();
      integration.integrationName = createDto.integrationName;
      integration.type = createDto.type;
      integration.userId = adminId; // Sử dụng adminId làm userId
      integration.info = createDto.info || null;
      integration.ownedType = OwnedTypeEnum.ADMIN; // Admin tạo thì ownedType = ADMIN

      // Lưu vào database
      const savedIntegration = await this.integrationRepository.save(integration);

      this.logger.log(`Admin ${adminId} đã tạo tích hợp ID: ${savedIntegration.id}`);

      // Chuyển đổi và trả về DTO
      return plainToInstance(IntegrationAdminResponseDto, savedIntegration, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      this.logger.error(`Lỗi khi admin tạo tích hợp: ${error.message}`, error.stack);
      throw new AppException(
        INTEGRATION_ERROR_CODES.INTEGRATION_CREATE_FAILED,
        `Lỗi khi tạo tích hợp: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách tích hợp cho admin
   * @param queryDto DTO chứa tham số truy vấn
   * @param adminId ID của admin
   * @returns Danh sách tích hợp với phân trang
   */
  async getIntegrations(
    queryDto: QueryIntegrationAdminDto,
    adminId: number,
  ): Promise<PaginatedResult<IntegrationAdminResponseDto>> {
    try {
      // Lấy danh sách tích hợp từ repository (chỉ của admin này)
      const queryParams = {
        ...queryDto,
        userId: adminId, // Filter theo adminId
      };
      const result = await this.integrationRepository.findAll(queryParams);

      // Chuyển đổi các phần tử sang DTO
      const items = result.items.map(integration =>
        plainToInstance(IntegrationAdminResponseDto, integration, {
          excludeExtraneousValues: true,
        }),
      );

      // Trả về kết quả với phân trang
      return {
        items,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi admin lấy danh sách tích hợp: ${error.message}`, error.stack);
      throw new AppException(
        INTEGRATION_ERROR_CODES.INTEGRATION_LIST_FAILED,
        `Lỗi khi lấy danh sách tích hợp: ${error.message}`,
      );
    }
  }

  /**
   * Lấy thông tin tích hợp theo ID cho admin
   * @param id ID của tích hợp
   * @param adminId ID của admin
   * @returns Thông tin chi tiết của tích hợp
   */
  async getIntegrationById(id: number, adminId: number): Promise<IntegrationAdminResponseDto> {
    try {
      // Lấy thông tin tích hợp từ repository (chỉ của admin này)
      const integration = await this.integrationRepository.findById(id, adminId);
      
      // Kiểm tra tích hợp tồn tại
      if (!integration) {
        throw new AppException(
          INTEGRATION_ERROR_CODES.INTEGRATION_NOT_FOUND,
          `Không tìm thấy tích hợp với ID ${id}`,
        );
      }

      // Chuyển đổi và trả về DTO
      return plainToInstance(IntegrationAdminResponseDto, integration, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      
      this.logger.error(`Lỗi khi admin lấy thông tin tích hợp: ${error.message}`, error.stack);
      throw new AppException(
        INTEGRATION_ERROR_CODES.INTEGRATION_LIST_FAILED,
        `Lỗi khi lấy thông tin tích hợp: ${error.message}`,
      );
    }
  }

  /**
   * Cập nhật thông tin tích hợp bởi admin
   * @param id ID của tích hợp
   * @param updateDto DTO chứa thông tin cập nhật
   * @param adminId ID của admin thực hiện cập nhật
   * @returns Thông tin tích hợp đã cập nhật
   */
  @Transactional()
  async updateIntegration(
    id: number,
    updateDto: UpdateIntegrationAdminDto,
    adminId: number,
  ): Promise<IntegrationAdminResponseDto> {
    try {
      // Lấy thông tin tích hợp hiện tại (chỉ của admin này)
      const integration = await this.integrationRepository.findById(id, adminId);
      
      // Kiểm tra tích hợp tồn tại
      if (!integration) {
        throw new AppException(
          INTEGRATION_ERROR_CODES.INTEGRATION_NOT_FOUND,
          `Không tìm thấy tích hợp với ID ${id}`,
        );
      }

      // Cập nhật thông tin
      if (updateDto.integrationName !== undefined) {
        integration.integrationName = updateDto.integrationName;
      }

      if (updateDto.type !== undefined) {
        integration.type = updateDto.type;
      }

      if (updateDto.info !== undefined) {
        integration.info = updateDto.info;
      }

      // Lưu vào database
      const updatedIntegration = await this.integrationRepository.save(integration);

      this.logger.log(`Admin ${adminId} đã cập nhật tích hợp ID: ${id}`);

      // Chuyển đổi và trả về DTO
      return plainToInstance(IntegrationAdminResponseDto, updatedIntegration, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      
      this.logger.error(`Lỗi khi admin cập nhật tích hợp: ${error.message}`, error.stack);
      throw new AppException(
        INTEGRATION_ERROR_CODES.INTEGRATION_UPDATE_FAILED,
        `Lỗi khi cập nhật tích hợp: ${error.message}`,
      );
    }
  }

  /**
   * Xóa tích hợp bởi admin
   * @param id ID của tích hợp
   * @param adminId ID của admin thực hiện xóa
   * @returns Kết quả xóa
   */
  @Transactional()
  async deleteIntegration(
    id: number,
    adminId: number,
  ): Promise<{ success: boolean; message: string }> {
    try {
      // Lấy thông tin tích hợp hiện tại (chỉ của admin này)
      const integration = await this.integrationRepository.findById(id, adminId);
      
      // Kiểm tra tích hợp tồn tại
      if (!integration) {
        throw new AppException(
          INTEGRATION_ERROR_CODES.INTEGRATION_NOT_FOUND,
          `Không tìm thấy tích hợp với ID ${id}`,
        );
      }

      // Xóa tích hợp
      await this.integrationRepository.delete(id);

      this.logger.log(`Admin ${adminId} đã xóa tích hợp ID: ${id}`);

      return {
        success: true,
        message: 'Xóa tích hợp thành công',
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      
      this.logger.error(`Lỗi khi admin xóa tích hợp: ${error.message}`, error.stack);
      throw new AppException(
        INTEGRATION_ERROR_CODES.INTEGRATION_DELETE_FAILED,
        `Lỗi khi xóa tích hợp: ${error.message}`,
      );
    }
  }
}
