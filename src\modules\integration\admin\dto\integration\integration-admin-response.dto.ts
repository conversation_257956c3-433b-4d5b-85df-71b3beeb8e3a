import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { OwnedTypeEnum } from '@modules/integration/enums';

/**
 * DTO cho phản hồi thông tin tích hợp từ admin
 */
export class IntegrationAdminResponseDto {
  /**
   * ID của tích hợp
   * @example 1
   */
  @ApiProperty({
    description: 'ID của tích hợp',
    example: 1
  })
  @Expose()
  id: number;

  /**
   * Tên của tích hợp
   * @example "Google Analytics Integration"
   */
  @ApiProperty({
    description: 'Tên của tích hợp',
    example: 'Google Analytics Integration'
  })
  @Expose()
  integrationName: string;

  /**
   * <PERSON><PERSON><PERSON> tích hợp
   * @example "ANALYTICS"
   */
  @ApiProperty({
    description: 'Loại tích hợp',
    example: 'ANALYTICS'
  })
  @Expose()
  type: string;

  /**
   * ID của người dùng
   * @example 1
   */
  @ApiProperty({
    description: 'ID của người dùng',
    example: 1
  })
  @Expose()
  userId: number;

  /**
   * <PERSON>ại chủ sở hữu tích hợp
   * @example "USER"
   */
  @ApiProperty({
    description: 'Loại chủ sở hữu tích hợp',
    enum: OwnedTypeEnum,
    example: OwnedTypeEnum.USER
  })
  @Expose()
  ownedType: OwnedTypeEnum;

  /**
   * Thông tin bổ sung của tích hợp
   * @example { "apiKey": "GA-12345", "propertyId": "123456789" }
   */
  @ApiProperty({
    description: 'Thông tin bổ sung của tích hợp',
    example: { apiKey: 'GA-12345', propertyId: '123456789' }
  })
  @Expose()
  info: Record<string, any> | null;

  /**
   * Thời điểm tạo tích hợp
   * @example "2024-01-15T10:30:00.000Z"
   */
  @ApiProperty({
    description: 'Thời điểm tạo tích hợp',
    type: 'string',
    format: 'date-time',
    example: '2024-01-15T10:30:00.000Z'
  })
  @Expose()
  createdAt: Date;
}
