import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsEnum, IsBoolean } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto, SortDirection } from '@common/dto/query.dto';

/**
 * Enum cho các trường sắp xếp của Facebook Page trong agent
 */
export enum AgentFacebookPageSortBy {
  PAGE_NAME = 'pageName',
  CREATED_AT = 'createdAt',
}

/**
 * DTO cho query parameters khi lấy danh sách Facebook Page trong agent
 */
export class AgentFacebookPageQueryDto extends QueryDto {
  /**
   * Sắp xếp theo trường
   */
  @ApiPropertyOptional({
    description: 'Sắp xếp theo trường',
    enum: AgentFacebookPageSortBy,
    default: AgentFacebookPageSortBy.CREATED_AT,
  })
  @IsOptional()
  @IsEnum(AgentFacebookPageSortBy)
  sortBy?: AgentFacebookPageSortBy = AgentFacebookPageSortBy.CREATED_AT;

  /**
   * Hướng sắp xếp
   */
  @ApiPropertyOptional({
    description: 'Hướng sắp xếp',
    enum: SortDirection,
    default: SortDirection.DESC,
  })
  @IsOptional()
  @IsEnum(SortDirection)
  @Type(() => String)
  sortDirection?: SortDirection = SortDirection.DESC;
}
