import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { ProductTypeEnum } from '@modules/business/enums';
import { BaseAdvancedProductResponseDto } from '../base/base-product-response.dto';

/**
 * Response DTO cho combo item
 */
export class ComboItemResponseDto {
  @ApiProperty({
    description: 'ID của sản phẩm trong combo',
    example: 123,
  })
  @Expose()
  productId: number;

  @ApiProperty({
    description: 'Số lượng sản phẩm trong combo',
    example: 2,
  })
  @Expose()
  total: number;

  @ApiProperty({
    description: 'Thông tin chi tiết sản phẩm',
    example: {
      id: 123,
      name: 'Áo thun nam',
      productType: 'PHYSICAL',
      price: {
        listPrice: 300000,
        salePrice: 250000,
        currency: 'VND'
      },
      images: [
        {
          key: 'product-image-0-1704067200000',
          position: 0,
          url: 'https://cdn.example.com/images/product-image-0-1704067200000.jpg'
        }
      ]
    },
    required: false,
  })
  @Expose()
  productDetails?: any;
}

/**
 * Response DTO cho thông tin nâng cao của combo
 */
export class ComboAdvancedInfoResponseDto {
  @ApiProperty({
    description: 'Số lượt mua',
    example: 50,
  })
  @Expose()
  purchaseCount: number;

  @ApiProperty({
    description: 'Danh sách sản phẩm trong combo',
    type: [ComboItemResponseDto],
  })
  @Expose()
  @Type(() => ComboItemResponseDto)
  info: ComboItemResponseDto[];

  @ApiProperty({
    description: 'Hình ảnh nâng cao',
    type: [Object],
    required: false,
  })
  @Expose()
  images?: any[];

  @ApiProperty({
    description: 'Tổng giá trị combo (tính từ các sản phẩm con)',
    example: {
      originalTotalPrice: 1000000,
      discountAmount: 100000,
      finalTotalPrice: 900000,
      currency: 'VND'
    },
    required: false,
  })
  @Expose()
  calculatedPrice?: {
    originalTotalPrice: number;
    discountAmount: number;
    finalTotalPrice: number;
    currency: string;
  };

  @ApiProperty({
    description: 'Thông tin tiết kiệm',
    example: {
      savingAmount: 100000,
      savingPercentage: 10,
      currency: 'VND'
    },
    required: false,
  })
  @Expose()
  savingInfo?: {
    savingAmount: number;
    savingPercentage: number;
    currency: string;
  };
}

/**
 * Response DTO cho sản phẩm combo (COMBO)
 * Kế thừa từ BaseAdvancedProductResponseDto và thêm các trường đặc thù cho combo
 */
export class ComboProductResponseDto extends BaseAdvancedProductResponseDto {
  @ApiProperty({
    description: 'Loại sản phẩm - luôn là COMBO',
    enum: ProductTypeEnum,
    example: ProductTypeEnum.COMBO,
  })
  @Expose()
  declare productType: ProductTypeEnum.COMBO;

  @ApiProperty({
    description: 'Thông tin nâng cao của sản phẩm combo',
    type: ComboAdvancedInfoResponseDto,
    required: false,
  })
  @Expose()
  @Type(() => ComboAdvancedInfoResponseDto)
  declare advancedInfo?: ComboAdvancedInfoResponseDto;

  @ApiProperty({
    description: 'Cấu hình vận chuyển (được tính toán từ các sản phẩm trong combo)',
    example: {
      widthCm: 30,
      heightCm: 10,
      lengthCm: 40,
      weightGram: 500
    },
  })
  @Expose()
  declare shipmentConfig: {
    widthCm: number;
    heightCm: number;
    lengthCm: number;
    weightGram: number;
  };

  @ApiProperty({
    description: 'Số lượng sản phẩm trong combo',
    example: 3,
  })
  @Expose()
  totalItems: number;

  @ApiProperty({
    description: 'Trạng thái có sẵn của combo (dựa trên tất cả sản phẩm con)',
    example: true,
  })
  @Expose()
  isAvailable: boolean;
}
