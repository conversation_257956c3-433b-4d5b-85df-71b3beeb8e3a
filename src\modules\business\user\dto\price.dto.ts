import { ApiProperty } from '@nestjs/swagger';
import {
  IsNumber,
  IsNotEmpty,
  IsString,
} from 'class-validator';

/**
 * DTO cho giá sản phẩm khi typePrice là HAS_PRICE
 */
export class HasPriceDto {
  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> niêm yết',
    example: 200000,
  })
  @IsNumber()
  @IsNotEmpty()
  listPrice: number;

  @ApiProperty({
    description: '<PERSON>i<PERSON> bán',
    example: 150000,
  })
  @IsNumber()
  @IsNotEmpty()
  salePrice: number;

  @ApiProperty({
    description: 'Đơn vị tiền tệ',
    example: 'VND',
  })
  @IsString()
  @IsNotEmpty()
  currency: string;
}

/**
 * DTO cho giá sản phẩm khi typePrice là STRING_PRICE
 */
export class StringPriceDto {
  @ApiProperty({
    description: '<PERSON>ô tả giá',
    example: '<PERSON><PERSON><PERSON> chưa công bố',
  })
  @IsString()
  @IsNotEmpty()
  priceDescription: string;
}
