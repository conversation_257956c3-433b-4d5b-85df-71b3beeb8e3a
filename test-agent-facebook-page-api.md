# Test API Agent Facebook Page với <PERSON>ân Trang

## API Endpoint
```
GET /user/agents/{id}/facebook-pages
```

## Query Parameters
- `page` (number, optional): Trang hiện tại (default: 1)
- `limit` (number, optional): Số lượng item trên mỗi trang (default: 10, max: 100)
- `search` (string, optional): T<PERSON> khóa tìm kiếm theo tên Facebook Page
- `isActive` (boolean, optional): L<PERSON><PERSON> theo trạng thái hoạt động
- `sortBy` (enum, optional): Tr<PERSON><PERSON><PERSON> sắp xếp (pageName, createdAt, isActive) - default: createdAt
- `sortDirection` (enum, optional): Hướng sắp xếp (ASC, DESC) - default: DESC

## Ví dụ Request

### 1. Lấy trang đầu tiên với 10 item
```
GET /user/agents/a1b2c3d4-e5f6-7890-abcd-ef1234567890/facebook-pages?page=1&limit=10
```

### 2. <PERSON><PERSON><PERSON><PERSON><PERSON> Facebook Page theo tên
```
GET /user/agents/a1b2c3d4-e5f6-7890-abcd-ef1234567890/facebook-pages?search=MyPage&page=1&limit=10
```

### 3. Lọc Facebook Page đang hoạt động và sắp xếp theo tên
```
GET /user/agents/a1b2c3d4-e5f6-7890-abcd-ef1234567890/facebook-pages?isActive=true&sortBy=pageName&sortDirection=ASC
```

## Response Format
```json
{
  "success": true,
  "message": "Thành công",
  "data": {
    "items": [
      {
        "id": "facebook-page-uuid-1",
        "avatarPage": "https://example.com/avatar.jpg",
        "pageName": "My Facebook Page",
        "isActive": true
      }
    ],
    "meta": {
      "totalItems": 25,
      "itemCount": 10,
      "itemsPerPage": 10,
      "totalPages": 3,
      "currentPage": 1,
      "hasItems": true
    }
  }
}
```

## Các Thay Đổi Đã Thực Hiện

### 1. Tạo AgentFacebookPageQueryDto
- Kế thừa từ QueryDto (page, limit, search)
- Thêm sortBy, sortDirection, isActive
- Validation đầy đủ với class-validator

### 2. Cập nhật FacebookPageRepository
- Thêm method `findByAgentIdWithPagination()`
- Hỗ trợ tìm kiếm theo pageName
- Lọc theo trạng thái isActive
- Sắp xếp theo các trường khác nhau
- Trả về PaginatedResult với hasItems
- Thêm method `getFacebookPageSortField()` để mapping sort fields

### 3. Cập nhật AgentFacebookPageService
- Method `getFacebookPages()` nhận thêm queryDto
- Sử dụng repository method mới
- Trả về PaginatedResult<AgentFacebookPageDto>
- Loại bỏ AgentFacebookPageListDto không sử dụng

### 4. Cập nhật AgentFacebookPageController
- Nhận @Query() queryDto: AgentFacebookPageQueryDto
- Sử dụng ApiResponseDto.paginated()
- Cập nhật Swagger documentation
- Sử dụng getPaginatedSchema cho response type

## So Sánh Trước và Sau

### Trước (Không phân trang):
```typescript
async getFacebookPages(agentId: string, userId: number): Promise<AgentFacebookPageListDto>
```

### Sau (Có phân trang):
```typescript
async getFacebookPages(
  agentId: string, 
  userId: number, 
  queryDto: AgentFacebookPageQueryDto
): Promise<PaginatedResult<AgentFacebookPageDto>>
```

## Lợi Ích
1. **Phân trang**: Không load tất cả Facebook Page cùng lúc
2. **Tìm kiếm**: Tìm theo tên Facebook Page
3. **Lọc**: Theo trạng thái hoạt động
4. **Sắp xếp**: Theo nhiều trường khác nhau
5. **hasItems**: Biết có dữ liệu hay không
6. **Performance**: Chỉ select các trường cần thiết
7. **Consistency**: Sử dụng chuẩn phân trang của hệ thống
8. **Error Handling**: Repository throw error thay vì return [] khi có lỗi
