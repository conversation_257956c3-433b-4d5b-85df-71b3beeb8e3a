import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsObject, IsOptional, IsString } from 'class-validator';

/**
 * DTO cho việc tạo mới tích hợp bởi admin
 */
export class CreateIntegrationAdminDto {
  /**
   * Tên của tích hợp
   * @example "Google Analytics Integration"
   */
  @ApiProperty({
    description: 'Tên của tích hợp',
    example: 'Google Analytics Integration',
    required: true
  })
  @IsNotEmpty({ message: 'Tên tích hợp không được để trống' })
  @IsString({ message: 'Tên tích hợp phải là chuỗi' })
  integrationName: string;

  /**
   * <PERSON><PERSON><PERSON> tích hợp
   * @example "ANALYTICS"
   */
  @ApiProperty({
    description: 'Loại tích hợp',
    example: 'ANALYTICS',
    required: true
  })
  @IsNotEmpty({ message: '<PERSON><PERSON><PERSON> tích hợp không được để trống' })
  @IsString({ message: '<PERSON><PERSON><PERSON> tích hợp phải là chuỗi' })
  type: string;

  /**
   * Thông tin bổ sung của tích hợp
   * @example { "apiKey": "GA-12345", "propertyId": "123456789" }
   */
  @ApiProperty({
    description: 'Thông tin bổ sung của tích hợp',
    example: { apiKey: 'GA-12345', propertyId: '123456789' },
    required: false
  })
  @IsOptional()
  @IsObject({ message: 'Thông tin tích hợp phải là đối tượng JSON' })
  info?: Record<string, any>;
}
