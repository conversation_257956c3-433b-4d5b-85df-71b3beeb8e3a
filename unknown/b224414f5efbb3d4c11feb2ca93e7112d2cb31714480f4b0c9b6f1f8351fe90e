import { Body, Controller, Post, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiExtraModels, ApiOperation, ApiProperty, ApiResponse, ApiTags } from '@nestjs/swagger';
import { IsOptional, IsString, IsNotEmpty } from 'class-validator';
import { JwtEmployeeGuard } from '@modules/auth/guards';
import { ApiResponseDto } from '@common/response';
import { FptSmsProvider } from '@shared/services/sms/fpt-sms-provider.service';
import { ConnectionTestResponse, SmsResponse } from '@shared/services/sms/sms-provider.interface';
import { SWAGGER_API_TAGS } from '@/common/swagger/swagger.tags';

/**
 * DTO cho việc test kết nối FPT SMS
 */
class TestFptSmsConnectionDto {
  /**
   * Client ID dùng cho xác thực O<PERSON>uth
   * @example "your-client-id"
   */
  @ApiProperty({
    description: 'Client ID dùng cho xác thực OAuth',
    example: 'your-client-id',
    required: false
  })
  @IsOptional()
  @IsString({ message: 'Client ID phải là chuỗi' })
  clientId?: string;

  /**
   * Client Secret dùng cho xác thực OAuth
   * @example "your-client-secret"
   */
  @ApiProperty({
    description: 'Client Secret dùng cho xác thực OAuth',
    example: 'your-client-secret',
    required: false
  })
  @IsOptional()
  @IsString({ message: 'Client Secret phải là chuỗi' })
  clientSecret?: string;

  /**
   * Phạm vi quyền truy cập cho xác thực OAuth
   * @example "send_brandname_otp send_brandname"
   */
  @ApiProperty({
    description: 'Phạm vi quyền truy cập cho xác thực OAuth',
    example: 'send_brandname_otp send_brandname',
    required: false
  })
  @IsOptional()
  @IsString({ message: 'Scope phải là chuỗi' })
  scope?: string;

  /**
   * Host API của FPT SMS (SMS_HOST_API)
   * @example "http://api.fpt.net/api"
   */
  @ApiProperty({
    description: 'Host API của FPT SMS (SMS_HOST_API)',
    example: 'http://api.fpt.net/api',
    required: false
  })
  @IsOptional()
  @IsString({ message: 'Host API phải là chuỗi' })
  hostApi?: string;
}

/**
 * DTO cho việc test gửi SMS qua FPT SMS
 */
class TestFptSmsSendDto {
  /**
   * Số điện thoại người nhận
   * @example "0901234567"
   */
  @ApiProperty({
    description: 'Số điện thoại người nhận',
    example: '0901234567',
    required: true
  })
  @IsNotEmpty({ message: 'Số điện thoại không được để trống' })
  @IsString({ message: 'Số điện thoại phải là chuỗi' })
  phoneNumber: string;

  /**
   * Nội dung tin nhắn
   * @example "Đây là tin nhắn test từ hệ thống"
   */
  @ApiProperty({
    description: 'Nội dung tin nhắn',
    example: 'Đây là tin nhắn test từ hệ thống',
    required: true
  })
  message: string;

  /**
   * Tên thương hiệu (brandname)
   * @example "FPTSHOP"
   */
  @ApiProperty({
    description: 'Tên thương hiệu (brandname)',
    example: 'FPTSHOP',
    required: false
  })
  brandName?: string;
}

/**
 * DTO cho việc test gửi OTP qua FPT SMS
 */
class TestFptSmsOtpDto {
  /**
   * Số điện thoại người nhận
   * @example "0901234567"
   */
  @ApiProperty({
    description: 'Số điện thoại người nhận',
    example: '0901234567',
    required: true
  })
  phoneNumber: string;

  /**
   * Mã OTP cần gửi
   * @example "123456"
   */
  @ApiProperty({
    description: 'Mã OTP cần gửi',
    example: '123456',
    required: true
  })
  otpCode: string;

  /**
   * Tên thương hiệu (brandname)
   * @example "FPTSHOP"
   */
  @ApiProperty({
    description: 'Tên thương hiệu (brandname)',
    example: 'FPTSHOP',
    required: false
  })
  brandName?: string;

  /**
   * Mẫu tin nhắn OTP
   * @example "Mã xác thực của bạn là: {code}. Vui lòng không chia sẻ mã này cho người khác."
   */
  @ApiProperty({
    description: 'Mẫu tin nhắn OTP',
    example: 'Mã xác thực của bạn là: {code}. Vui lòng không chia sẻ mã này cho người khác.',
    required: false
  })
  template?: string;
}

/**
 * DTO cho việc test kiểm tra trạng thái tin nhắn
 */
class TestFptSmsStatusDto {
  /**
   * ID của tin nhắn cần kiểm tra
   * @example "123456789"
   */
  @ApiProperty({
    description: 'ID của tin nhắn cần kiểm tra',
    example: '123456789',
    required: true
  })
  messageId: string;
}

/**
 * Controller quản lý các API test SMS
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_INTEGRATION_SMS_TEST)
@Controller('admin/integration/sms-test')
@UseGuards(JwtEmployeeGuard)
@ApiBearerAuth('JWT-auth')
@ApiExtraModels(ApiResponseDto)
export class SmsTestAdminController {
  constructor(private readonly fptSmsProvider: FptSmsProvider) {}

  /**
   * Test kết nối với FPT SMS
   */
  @Post('fpt/test-connection')
  @ApiOperation({ summary: 'Test kết nối với FPT SMS' })
  @ApiResponse({
    status: 200,
    description: 'Kết quả test kết nối',
    schema: ApiResponseDto.getSchema(Object),
  })
  async testFptSmsConnection(
    @Body() testDto: TestFptSmsConnectionDto,
  ): Promise<ApiResponseDto<ConnectionTestResponse>> {
    // Sử dụng giá trị mặc định từ biến môi trường nếu không được cung cấp
    const result = await this.fptSmsProvider.testConnection({
      clientId: testDto.clientId || '',
      clientSecret: testDto.clientSecret || '',
      scope: testDto.scope,
      hostApi: testDto.hostApi || 'http://api.fpt.net/api',
    });

    return ApiResponseDto.success(result);
  }

  /**
   * Test gửi SMS qua FPT SMS
   */
  @Post('fpt/send-sms')
  @ApiOperation({ summary: 'Test gửi SMS qua FPT SMS' })
  @ApiResponse({
    status: 200,
    description: 'Kết quả gửi SMS',
    schema: ApiResponseDto.getSchema(Object),
  })
  async testFptSmsSend(
    @Body() testDto: TestFptSmsSendDto,
  ): Promise<ApiResponseDto<SmsResponse>> {
    const result = await this.fptSmsProvider.sendSms(
      testDto.phoneNumber,
      testDto.message,
      { brandName: testDto.brandName },
    );

    return ApiResponseDto.success(result);
  }

  /**
   * Test gửi OTP qua FPT SMS
   */
  @Post('fpt/send-otp')
  @ApiOperation({ summary: 'Test gửi OTP qua FPT SMS' })
  @ApiResponse({
    status: 200,
    description: 'Kết quả gửi OTP',
    schema: ApiResponseDto.getSchema(Object),
  })
  async testFptSmsOtp(
    @Body() testDto: TestFptSmsOtpDto,
  ): Promise<ApiResponseDto<SmsResponse>> {
    const result = await this.fptSmsProvider.sendOtp(
      testDto.phoneNumber,
      testDto.otpCode,
      {
        brandName: testDto.brandName,
        template: testDto.template,
      },
    );

    return ApiResponseDto.success(result);
  }

  /**
   * Test kiểm tra trạng thái tin nhắn
   */
  @Post('fpt/check-status')
  @ApiOperation({ summary: 'Test kiểm tra trạng thái tin nhắn' })
  @ApiResponse({
    status: 200,
    description: 'Kết quả kiểm tra trạng thái',
    schema: ApiResponseDto.getSchema(Object),
  })
  async testFptSmsStatus(
    @Body() testDto: TestFptSmsStatusDto,
  ): Promise<ApiResponseDto<any>> {
    const result = await this.fptSmsProvider.checkMessageStatus(testDto.messageId);

    return ApiResponseDto.success(result);
  }
}
