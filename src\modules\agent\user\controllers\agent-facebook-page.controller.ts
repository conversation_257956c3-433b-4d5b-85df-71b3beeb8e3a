import { SWAGGER_API_TAGS } from '@/common/swagger';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { ErrorCode } from '@common/exceptions';
import { ApiResponseDto } from '@common/response';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import { CurrentUser } from '@modules/auth/decorators';
import { JwtUserGuard } from '@modules/auth/guards';
import { INTEGRATION_ERROR_CODES } from '@modules/integration/exceptions';
import { Body, Controller, Delete, Get, Param, Post, UseGuards, Query } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import {
  AgentFacebookPageDto,
  AgentFacebookPageQueryDto,
  IntegrateFacebookPageDto,
  IntegrateFacebookPagesResponseDto
} from '../dto/facebook-page';
import { AgentFacebookPageService } from '../services/agent-facebook-page.service';

/**
 * Controller xử lý các API liên quan đến tích hợp Facebook Page với Agent
 */
@ApiTags(SWAGGER_API_TAGS.USER_AGENT)
@Controller('user/agents/:id/facebook-pages')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class AgentFacebookPageController {
  constructor(private readonly agentFacebookPageService: AgentFacebookPageService) { }

  /**
   * API tích hợp danh sách Facebook Page vào Agent
   * @param agentId ID của Agent
   * @param dto Danh sách Facebook Page cần tích hợp
   * @param userId ID của người dùng hiện tại
   * @returns Thông báo tích hợp thành công
   */
  @Post()
  @ApiOperation({ summary: 'Tích hợp danh sách Facebook Page vào Agent' })
  @ApiParam({
    name: 'id',
    description: 'ID của Agent',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890'
  })
  @ApiResponse({
    status: 200,
    description: 'Tích hợp danh sách Facebook Page thành công',
    schema: ApiResponseDto.getSchema(IntegrateFacebookPagesResponseDto)
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_NOT_FOUND,
    INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_NOT_FOUND,
    INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_ALREADY_INTEGRATED,
    INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_SUBSCRIBE_FAILED,
    INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_INTEGRATION_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async integrateFacebookPages(
    @Param('id') agentId: string,
    @Body() dto: IntegrateFacebookPageDto,
    @CurrentUser('id') userId: number
  ) {
    const result = await this.agentFacebookPageService.integrateFacebookPages(agentId, userId, dto);
    return ApiResponseDto.success(result);
  }

  /**
   * API lấy danh sách Facebook Page trong Agent với phân trang
   * @param agentId ID của Agent
   * @param queryDto Tham số truy vấn và phân trang
   * @param userId ID của người dùng hiện tại
   * @returns Danh sách Facebook Page với phân trang
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách Facebook Page trong Agent với phân trang' })
  @ApiParam({
    name: 'id',
    description: 'ID của Agent',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890'
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách Facebook Page thành công',
    schema: ApiResponseDto.getPaginatedSchema(AgentFacebookPageDto)
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_NOT_FOUND,
    INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_LIST_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async getFacebookPages(
    @Param('id') agentId: string,
    @Query() queryDto: AgentFacebookPageQueryDto,
    @CurrentUser('id') userId: number
  ) {
    const result = await this.agentFacebookPageService.getFacebookPages(agentId, userId, queryDto);
    return ApiResponseDto.paginated(result);
  }

  /**
   * API gỡ Facebook Page khỏi Agent
   * @param agentId ID của Agent
   * @param pageId UUID của Facebook Page trong hệ thống
   * @param userId ID của người dùng hiện tại
   * @returns Thông báo gỡ bỏ thành công
   */
  @Delete(':pageId')
  @ApiOperation({ summary: 'Gỡ Facebook Page khỏi Agent' })
  @ApiParam({
    name: 'agentId',
    description: 'ID của Agent',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890'
  })
  @ApiParam({
    name: 'pageId',
    description: 'UUID của Facebook Page trong hệ thống',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890'
  })
  @ApiResponse({
    status: 200,
    description: 'Gỡ Facebook Page thành công',
    schema: ApiResponseDto.getSchema('Gỡ Facebook Page thành công')
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_NOT_FOUND,
    INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_NOT_INTEGRATED,
    INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_REMOVE_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async removeFacebookPage(
    @Param('id') agentId: string,
    @Param('pageId') pageId: string,
    @CurrentUser('id') userId: number
  ) {
    await this.agentFacebookPageService.removeFacebookPage(agentId, pageId, userId);
    return ApiResponseDto.success('Gỡ Facebook Page thành công');
  }
}
