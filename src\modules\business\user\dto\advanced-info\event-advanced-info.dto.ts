import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsString,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsArray,
  IsObject,
  ValidateNested,
  IsEnum,
  Min,
} from 'class-validator';
import { EventFormatEnum } from '@modules/business/enums';
import { CurrencyType } from '../currency/currency.type';

/**
 * DTO cho Ticket Type
 */
export class TicketTypeDto {
  @ApiProperty({
    description: 'Tên loại vé',
    example: 'Vé thường',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Giá vé',
    example: 400000,
  })
  @IsNumber()
  @Min(0)
  price: number;

  @ApiProperty({
    description: 'Đơn vị tiền tệ',
    example: 'VND',
  })
  @IsString()
  @IsNotEmpty()
  currency: CurrencyType;

  @ApiProperty({
    description: 'Số lượng vé',
    example: 100,
  })
  @IsNumber()
  @Min(1)
  quantity: number;

  @ApiProperty({
    description: 'Mã SKU',
    example: 'TICKET-001',
  })
  @IsString()
  @IsNotEmpty()
  sku: string;

  @ApiProperty({
    description: 'Thời gian bắt đầu (timestamp)',
    example: 1704067200000,
  })
  @IsNumber()
  startTime: number;

  @ApiProperty({
    description: 'Thời gian kết thúc (timestamp)',
    example: 1704153600000,
  })
  @IsNumber()
  endTime: number;

  @ApiProperty({
    description: 'Múi giờ',
    example: 'Asia/Ho_Chi_Minh',
  })
  @IsString()
  @IsNotEmpty()
  timezone: string;

  @ApiProperty({
    description: 'Mô tả vé',
    example: 'Vé tham gia hội thảo cơ bản',
  })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({
    description: 'Số lượng tối thiểu mỗi lần mua',
    example: 1,
  })
  @IsNumber()
  @Min(1)
  minQuantityPerPurchase: number;

  @ApiProperty({
    description: 'Số lượng tối đa mỗi lần mua',
    example: 5,
  })
  @IsNumber()
  @Min(1)
  maxQuantityPerPurchase: number;

  @ApiProperty({
    description: 'Danh sách loại hình ảnh',
    type: [String],
    example: ['image/jpeg'],
  })
  @IsArray()
  @IsString({ each: true })
  imagesMediaTypes: string[];
}

/**
 * DTO cho Event Advanced Info
 */
export class EventAdvancedInfoDto {
  @ApiProperty({
    description: 'Hình thức sự kiện',
    example: EventFormatEnum.ONLINE,
    enum: EventFormatEnum,
  })
  @IsEnum(EventFormatEnum)
  @IsNotEmpty()
  eventFormat: EventFormatEnum;

  @ApiProperty({
    description: 'Link tham gia sự kiện',
    example: 'https://zoom.us/j/123456789',
    required: false,
  })
  @IsOptional()
  @IsString()
  eventLink?: string;

  @ApiProperty({
    description: 'Địa điểm sự kiện',
    example: 'Trung tâm Hội nghị Quốc gia, Hà Nội',
    required: false,
  })
  @IsOptional()
  @IsString()
  eventLocation?: string;

  @ApiProperty({
    description: 'Thời gian bắt đầu (timestamp)',
    example: 1704067200000,
  })
  @IsNumber()
  startDate: number;

  @ApiProperty({
    description: 'Thời gian kết thúc (timestamp)',
    example: 1704153600000,
  })
  @IsNumber()
  endDate: number;

  @ApiProperty({
    description: 'Múi giờ',
    example: 'Asia/Ho_Chi_Minh',
  })
  @IsString()
  @IsNotEmpty()
  timezone: string;

  @ApiProperty({
    description: 'Danh sách loại vé',
    type: [TicketTypeDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TicketTypeDto)
  ticketTypes: TicketTypeDto[];
}
