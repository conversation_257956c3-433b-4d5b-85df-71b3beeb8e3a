import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsString,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsArray,
  IsObject,
  ValidateNested,
  IsEnum,
  Min,
} from 'class-validator';
import { EntityStatusEnum } from '@modules/business/enums';

/**
 * DTO cho Service Package
 */
export class ServicePackageDto {
  @ApiProperty({
    description: 'Tên gói dịch vụ',
    example: 'Gói tư vấn cơ bản',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: '<PERSON><PERSON> tả gói dịch vụ',
    example: 'Tư vấn chiến lược kinh doanh cơ bản',
  })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({
    description: 'Giá gói dịch vụ',
    example: 1800000,
  })
  @IsNumber()
  @Min(0)
  price: number;

  @ApiProperty({
    description: 'Thời lượng dịch vụ (phút)',
    example: 60,
  })
  @IsNumber()
  @Min(1)
  duration: number;

  @ApiProperty({
    description: 'Thời gian bắt đầu (timestamp)',
    example: 1704067200000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  startTime?: number;

  @ApiProperty({
    description: 'Thời gian kết thúc (timestamp)',
    example: 1704153600000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  endTime?: number;

  @ApiProperty({
    description: 'Số lượng',
    example: 10,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  quantity?: number;

  @ApiProperty({
    description: 'Số lượng tối thiểu mỗi lần mua',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  minQuantityPerPurchase?: number;

  @ApiProperty({
    description: 'Số lượng tối đa mỗi lần mua',
    example: 5,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  maxQuantityPerPurchase?: number;

  @ApiProperty({
    description: 'Múi giờ',
    example: 'Asia/Ho_Chi_Minh',
    required: false,
  })
  @IsOptional()
  @IsString()
  timezone?: string;

  @ApiProperty({
    description: 'Trạng thái gói dịch vụ',
    example: EntityStatusEnum.PENDING,
    enum: EntityStatusEnum,
    required: false,
  })
  @IsOptional()
  @IsEnum(EntityStatusEnum)
  status?: EntityStatusEnum;

  @ApiProperty({
    description: 'Danh sách loại hình ảnh',
    type: [String],
    example: ['image/jpeg'],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  imagesMediaTypes?: string[];
}

/**
 * DTO cho Service Advanced Info
 */
export class ServiceAdvancedInfoDto {
  @ApiProperty({
    description: 'Số lượt mua',
    example: 0,
  })
  @IsNumber()
  @Min(0)
  purchaseCount: number;

  @ApiProperty({
    description: 'Danh sách gói dịch vụ',
    type: [ServicePackageDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ServicePackageDto)
  servicePackages: ServicePackageDto[] = [];
}
