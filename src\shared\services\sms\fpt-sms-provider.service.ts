import { Injectable } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { BaseSmsProvider } from './base-sms-provider.service';
import { SmsResponse, BulkSmsResponse, MessageStatusResponse, ConnectionTestResponse, MessageStatus } from './sms-provider.interface';

/**
 * Interface cho cấu hình FPT SMS (để test connection)
 */
export interface FptSmsConfig {
  /**
   * Client ID dùng cho xác thực OAuth (SMS_CLIENT_ID)
   */
  clientId: string;

  /**
   * Client Secret dùng cho xác thực <PERSON>Auth (SMS_SECRET)
   */
  clientSecret: string;

  /**
   * Host API của FPT SMS (SMS_HOST_API) - từ endpoint database
   */
  hostApi?: string;

  /**
   * Phạm vi quyền truy cập cho xác thực <PERSON>
   */
  scope?: string;

  /**
   * Brandname mặc định
   */
  brandName?: string;
}

/**
 * Service tích hợp với API FPT SMS
 */
@Injectable()
export class FptSmsProvider extends BaseSmsProvider {
  readonly providerName = 'FPT SMS';

  private readonly apiUrl: string;
  private readonly clientId: string;
  private readonly clientSecret: string;
  private readonly scope: string;
  private readonly defaultBrandName: string;

  // Token truy cập và thời gian hết hạn của nó
  private accessToken: string | null = null;
  private tokenExpiration: Date | null = null;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    super('FptSmsProvider');

    // Tải cấu hình từ biến môi trường hoặc sử dụng giá trị mặc định
    this.clientId = this.configService.get<string>('FPT_SMS_CLIENT_ID') || '';
    this.clientSecret = this.configService.get<string>('FPT_SMS_CLIENT_SECRET') || '';
    this.scope = this.configService.get<string>('FPT_SMS_SCOPE') || 'send_brandname_otp send_brandname';
    this.apiUrl = this.configService.get<string>('FPT_SMS_API_URL') || 'http://api.fpt.net/api';
    this.defaultBrandName = this.configService.get<string>('FPT_SMS_BRANDNAME') || '';
  }

  /**
   * Lấy token truy cập hợp lệ cho API FPT SMS
   * @param forceRefresh Buộc làm mới token ngay cả khi nó vẫn còn hợp lệ
   * @returns Token truy cập
   */
  private async getAccessToken(forceRefresh = false): Promise<string> {
    // Kiểm tra xem token có còn hợp lệ không
    const now = new Date();
    if (!forceRefresh && this.accessToken && this.tokenExpiration && this.tokenExpiration > now) {
      // Token vẫn còn hợp lệ, trả về token
      return this.accessToken;
    }

    try {
      this.logger.debug('Lấy token truy cập mới cho FPT SMS');

      const response = await firstValueFrom(
        this.httpService.post(
          `${this.apiUrl}/oauth2/token`,
          {
            grant_type: 'client_credentials',
            client_id: this.clientId,
            client_secret: this.clientSecret,
            scope: this.scope,
            session_id: Date.now().toString()
          },
          {
            headers: {
              'Content-Type': 'application/json'
            }
          }
        ).pipe(
          catchError(error => {
            this.logger.error(`Erreur lors de l'obtention du token d'accès FPT SMS: ${error.message}`, error.stack);
            throw error;
          })
        )
      );

      if (response.data.error) {
        throw new Error(`Erreur FPT SMS: ${response.data.error_description}`);
      }

      // Lưu token và tính toán thời gian hết hạn của nó
      this.accessToken = response.data.access_token;
      this.tokenExpiration = new Date(now.getTime() + (response.data.expires_in * 1000));

      // Kiểm tra token không phải là null
      if (!this.accessToken) {
        throw new Error('Token truy cập FPT SMS là null');
      }

      return this.accessToken;
    } catch (error) {
      this.logger.error(`Lỗi khi lấy token truy cập FPT SMS: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Gửi tin nhắn SMS đến một số điện thoại qua FPT SMS
   * @param phoneNumber Số điện thoại của người nhận
   * @param message Nội dung tin nhắn
   * @param options Các tùy chọn bổ sung (brandName)
   * @returns Promise chứa ID tin nhắn hoặc lỗi
   */
  async sendSms(phoneNumber: string, message: string, options?: any): Promise<SmsResponse> {
    try {
      this.logger.debug(`Gửi SMS đến ${phoneNumber} qua FPT SMS`);

      const brandName = options?.brandName || this.defaultBrandName;

      if (!brandName) {
        throw new Error('Tham số "brandName" là bắt buộc đối với FPT SMS');
      }

      // Lấy token truy cập hợp lệ
      const accessToken = await this.getAccessToken();

      // Định dạng số điện thoại
      const formattedPhoneNumber = this.formatPhoneNumber(phoneNumber);
      // Xóa dấu + cho FPT SMS
      const cleanedPhoneNumber = formattedPhoneNumber.startsWith('+') ? formattedPhoneNumber.substring(1) : formattedPhoneNumber;

      // Gửi SMS qua FPT SMS
      const response = await firstValueFrom(
        this.httpService.post(
          `${this.apiUrl}/push-brandname-otp`,
          {
            access_token: accessToken,
            session_id: Date.now().toString(),
            BrandName: brandName,
            Phone: cleanedPhoneNumber,
            Message: message,
            Quota: options?.quota || 1
          },
          {
            headers: {
              'Content-Type': 'application/json'
            }
          }
        ).pipe(
          catchError(error => {
            this.logger.error(`Erreur lors de l'envoi du SMS via FPT SMS: ${error.message}`, error.stack);
            throw error;
          })
        )
      );

      if (response.data.errorid === 0) {
        return {
          success: true,
          messageId: response.data.requestid,
          rawResponse: response.data
        };
      } else {
        return {
          success: false,
          errorCode: response.data.errorid.toString(),
          errorMessage: response.data.errordes || 'Lỗi không xác định',
          rawResponse: response.data
        };
      }
    } catch (error) {
      this.logger.error(`Lỗi khi gửi SMS qua FPT SMS: ${error.message}`, error.stack);
      return {
        success: false,
        errorMessage: error.message || 'Lỗi không xác định'
      };
    }
  }

  /**
   * Kiểm tra trạng thái của tin nhắn đã gửi qua FPT SMS
   * @param messageId ID của tin nhắn cần kiểm tra
   * @returns Promise chứa trạng thái của tin nhắn
   */
  async checkMessageStatus(messageId: string): Promise<MessageStatusResponse> {
    try {
      this.logger.debug(`Kiểm tra trạng thái tin nhắn ${messageId} qua FPT SMS`);

      // Lấy token truy cập hợp lệ
      const accessToken = await this.getAccessToken();

      // Kiểm tra trạng thái tin nhắn qua FPT SMS
      const response = await firstValueFrom(
        this.httpService.post(
          `${this.apiUrl}/dlr-otp-recheck`,
          {
            access_token: accessToken,
            session_id: Date.now().toString(),
            RequestCode: messageId
          },
          {
            headers: {
              'Content-Type': 'application/json'
            }
          }
        ).pipe(
          catchError(error => {
            this.logger.error(`Erreur lors de la vérification du statut du message via FPT SMS: ${error.message}`, error.stack);
            throw error;
          })
        )
      );

      // Xác định trạng thái dựa trên phản hồi
      let status = MessageStatus.UNKNOWN;
      let details = '';

      if (response.data.errorid === 0) {
        status = MessageStatus.DELIVERED;
      } else {
        status = MessageStatus.FAILED;
        details = response.data.errordes || 'Lỗi không xác định';
      }

      return {
        messageId,
        status,
        updatedAt: new Date(),
        details,
        rawResponse: response.data
      };
    } catch (error) {
      this.logger.error(`Lỗi khi kiểm tra trạng thái tin nhắn qua FPT SMS: ${error.message}`, error.stack);
      return {
        messageId,
        status: MessageStatus.UNKNOWN,
        updatedAt: new Date(),
        details: error.message || 'Lỗi không xác định'
      };
    }
  }

  /**
   * Gửi tin nhắn SMS với brandname qua FPT SMS
   * @param phoneNumber Số điện thoại của người nhận
   * @param message Nội dung tin nhắn
   * @param brandname Tên thương hiệu sử dụng làm người gửi
   * @param options Các tùy chọn bổ sung
   * @returns Promise chứa ID tin nhắn hoặc lỗi
   */
  async sendBrandnameSms(phoneNumber: string, message: string, brandname: string, options?: any): Promise<SmsResponse> {
    try {
      this.logger.debug(`Gửi SMS brandname đến ${phoneNumber} qua FPT SMS`);

      // Lấy token truy cập hợp lệ
      const accessToken = await this.getAccessToken();

      // Định dạng số điện thoại
      const formattedPhoneNumber = this.formatPhoneNumber(phoneNumber);
      // Xóa dấu + cho FPT SMS
      const cleanedPhoneNumber = formattedPhoneNumber.startsWith('+') ? formattedPhoneNumber.substring(1) : formattedPhoneNumber;

      // Tạo chiến dịch cho SMS brandname
      const campaignResponse = await firstValueFrom(
        this.httpService.post(
          `${this.apiUrl}/create-campaign`,
          {
            access_token: accessToken,
            session_id: Date.now().toString(),
            CampaignName: options?.campaignName || `Campaign_${Date.now()}`,
            BrandName: brandname,
            Message: message,
            ScheduleTime: options?.scheduleTime || this.formatScheduleTime(new Date()),
            Quota: options?.quota || 1
          },
          {
            headers: {
              'Content-Type': 'application/json'
            }
          }
        ).pipe(
          catchError(error => {
            this.logger.error(`Erreur lors de la création de la campagne FPT SMS: ${error.message}`, error.stack);
            throw error;
          })
        )
      );

      if (campaignResponse.data.errorid !== 0) {
        return {
          success: false,
          errorCode: campaignResponse.data.errorid.toString(),
          errorMessage: campaignResponse.data.errordes || 'Lỗi khi tạo chiến dịch',
          rawResponse: campaignResponse.data
        };
      }

      // Gửi SMS brandname
      const adsResponse = await firstValueFrom(
        this.httpService.post(
          `${this.apiUrl}/push-ads`,
          {
            access_token: accessToken,
            session_id: Date.now().toString(),
            CampaignCode: campaignResponse.data.campaigncode,
            PhoneList: cleanedPhoneNumber
          },
          {
            headers: {
              'Content-Type': 'application/json'
            }
          }
        ).pipe(
          catchError(error => {
            this.logger.error(`Erreur lors de l'envoi du SMS brandname via FPT SMS: ${error.message}`, error.stack);
            throw error;
          })
        )
      );

      if (adsResponse.data.errorid === 0) {
        return {
          success: true,
          messageId: adsResponse.data.requestid,
          rawResponse: {
            campaign: campaignResponse.data,
            ads: adsResponse.data
          }
        };
      } else {
        return {
          success: false,
          errorCode: adsResponse.data.errorid.toString(),
          errorMessage: adsResponse.data.errordes || 'Lỗi không xác định',
          rawResponse: {
            campaign: campaignResponse.data,
            ads: adsResponse.data
          }
        };
      }
    } catch (error) {
      this.logger.error(`Lỗi khi gửi SMS brandname qua FPT SMS: ${error.message}`, error.stack);
      return {
        success: false,
        errorMessage: error.message || 'Lỗi không xác định'
      };
    }
  }

  /**
   * Gửi tin nhắn SMS OTP qua FPT SMS
   * @param phoneNumber Số điện thoại của người nhận
   * @param otpCode Mã OTP cần gửi
   * @param options Các tùy chọn bổ sung (brandName, template)
   * @returns Promise chứa ID tin nhắn hoặc lỗi
   */
  async sendOtp(phoneNumber: string, otpCode: string, options?: any): Promise<SmsResponse> {
    this.logger.debug(`Gửi mã OTP ${otpCode} đến số điện thoại ${phoneNumber} qua FPT SMS`);

    // Xây dựng tin nhắn OTP
    const message = options?.template
      ? options.template.replace('{code}', otpCode)
      : `Mã xác thực của bạn là: ${otpCode}`;

    // Sử dụng API push-brandname-otp để gửi OTP
    return this.sendSms(phoneNumber, message, options);
  }

  /**
   * Kiểm tra kết nối với FPT SMS
   * @param config Cấu hình của FPT SMS
   * @returns Promise chỉ ra liệu kết nối có thành công hay không
   */
  async testConnection(config: FptSmsConfig): Promise<ConnectionTestResponse> {
    try {
      this.logger.debug('Kiểm tra kết nối với FPT SMS');

      const clientId = config.clientId || this.clientId;
      const clientSecret = config.clientSecret || this.clientSecret;
      const scope = config.scope || this.scope;
      const apiUrl = config.hostApi || this.apiUrl;

      if (!clientId || !clientSecret) {
        return {
          success: false,
          message: 'Thiếu thông tin xác thực FPT SMS'
        };
      }

      // Kiểm tra việc lấy token truy cập
      const response = await firstValueFrom(
        this.httpService.post(
          `${apiUrl}/oauth2/token`,
          {
            grant_type: 'client_credentials',
            client_id: clientId,
            client_secret: clientSecret,
            scope: scope,
            session_id: Date.now().toString()
          },
          {
            headers: {
              'Content-Type': 'application/json'
            }
          }
        ).pipe(
          catchError(error => {
            this.logger.error(`Erreur lors du test de connexion avec FPT SMS: ${error.message}`, error.stack);
            throw error;
          })
        )
      );

      if (response.data.error) {
        return {
          success: false,
          message: response.data.error_description || 'Lỗi không xác định',
          details: response.data
        };
      }

      return {
        success: true,
        message: 'Kết nối thành công',
        details: {
          accessToken: response.data.access_token,
          expiresIn: response.data.expires_in,
          tokenType: response.data.token_type,
          scope: response.data.scope
        }
      };
    } catch (error) {
      this.logger.error(`Lỗi khi kiểm tra kết nối với FPT SMS: ${error.message}`, error.stack);
      return {
        success: false,
        message: error.message || 'Lỗi không xác định'
      };
    }
  }

  /**
   * Định dạng ngày tháng theo định dạng yêu cầu của FPT SMS (yyyy-MM-dd HH:mm)
   * @param date Ngày cần định dạng
   * @returns Chuỗi ngày đã định dạng
   */
  private formatScheduleTime(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}`;
  }
}
