import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { PriceTypeEnum, ProductTypeEnum, EntityStatusEnum } from '@modules/business/enums';

/**
 * Base Response DTO chung cho tất cả các loại sản phẩm
 * Chứa các trường dữ liệu chung mà mọi sản phẩm đều trả về
 */
export abstract class BaseProductResponseDto {
  @ApiProperty({
    description: 'ID sản phẩm',
    example: 123,
  })
  @Expose()
  id: number;

  @ApiProperty({
    description: 'Tên sản phẩm',
    example: 'Áo thun nam',
  })
  @Expose()
  name: string;

  @ApiProperty({
    description: 'Loại sản phẩm',
    enum: ProductTypeEnum,
    example: ProductTypeEnum.PHYSICAL,
  })
  @Expose()
  productType: ProductTypeEnum;

  @ApiProperty({
    description: 'Loại giá sản phẩm',
    enum: PriceTypeEnum,
    example: PriceTypeEnum.HAS_PRICE,
  })
  @Expose()
  typePrice: PriceTypeEnum;

  @ApiProperty({
    description: 'Thông tin giá sản phẩm',
    example: {
      listPrice: 300000,
      salePrice: 250000,
      currency: 'VND'
    },
    required: false,
  })
  @Expose()
  price?: any;

  @ApiProperty({
    description: 'Mô tả sản phẩm',
    example: 'Áo thun nam chất liệu cotton cao cấp',
    required: false,
  })
  @Expose()
  description?: string;

  @ApiProperty({
    description: 'Danh sách hình ảnh sản phẩm',
    type: [Object],
    example: [
      {
        key: 'product-image-0-1704067200000',
        position: 0,
        url: 'https://cdn.example.com/images/product-image-0-1704067200000.jpg'
      }
    ],
    required: false,
  })
  @Expose()
  images?: any[];

  @ApiProperty({
    description: 'Danh sách tags sản phẩm',
    type: [String],
    example: ['thời trang', 'nam', 'cotton'],
    required: false,
  })
  @Expose()
  tags?: string[];

  @ApiProperty({
    description: 'Metadata chứa thông tin bổ sung',
    example: {
      customFields: [],
      variants: []
    },
    required: false,
  })
  @Expose()
  metadata?: any;

  @ApiProperty({
    description: 'Cấu hình vận chuyển',
    example: {
      widthCm: 20,
      heightCm: 5,
      lengthCm: 25,
      weightGram: 150
    },
    required: false,
  })
  @Expose()
  shipmentConfig?: any;

  @ApiProperty({
    description: 'Trạng thái sản phẩm',
    enum: EntityStatusEnum,
    example: EntityStatusEnum.PENDING,
  })
  @Expose()
  status: EntityStatusEnum;

  @ApiProperty({
    description: 'Thời gian tạo (timestamp)',
    example: 1704067200000,
  })
  @Expose()
  createdAt: number;

  @ApiProperty({
    description: 'Thời gian cập nhật cuối (timestamp)',
    example: 1704067200000,
  })
  @Expose()
  updatedAt: number;

  @ApiProperty({
    description: 'ID người tạo sản phẩm',
    example: 1,
  })
  @Expose()
  createdBy: number;

  @ApiProperty({
    description: 'Danh sách phân loại sản phẩm',
    type: [Object],
    required: false,
  })
  @Expose()
  classifications?: any[];

  @ApiProperty({
    description: 'Thông tin upload URLs (chỉ có khi tạo mới)',
    example: {
      productId: '123',
      imagesUploadUrls: [
        {
          url: 'https://s3.amazonaws.com/presigned-url',
          key: 'product-image-0-1704067200000',
          index: 0
        }
      ]
    },
    required: false,
  })
  @Expose()
  uploadUrls?: any;
}

/**
 * Base Response DTO cho các sản phẩm có thông tin nâng cao
 * (DIGITAL, EVENT, SERVICE, COMBO)
 */
export abstract class BaseAdvancedProductResponseDto extends BaseProductResponseDto {
  @ApiProperty({
    description: 'Thông tin nâng cao của sản phẩm',
    required: false,
  })
  @Expose()
  advancedInfo?: any;

  @ApiProperty({
    description: 'Upload URLs cho advanced images (chỉ có khi tạo mới)',
    type: [Object],
    required: false,
  })
  @Expose()
  advancedImagesUploadUrls?: any[];
}

/**
 * Response DTO cho batch operations
 */
export class BatchProductResponseDto {
  @ApiProperty({
    description: 'Danh sách sản phẩm đã tạo thành công',
    type: [BaseProductResponseDto],
  })
  @Expose()
  @Type(() => BaseProductResponseDto)
  successProducts: BaseProductResponseDto[];

  @ApiProperty({
    description: 'Danh sách lỗi khi tạo sản phẩm',
    type: [Object],
    example: [
      {
        index: 1,
        error: 'Product name is required',
        productData: { name: '', productType: 'PHYSICAL' }
      }
    ],
    required: false,
  })
  @Expose()
  errors?: any[];

  @ApiProperty({
    description: 'Tổng số sản phẩm được xử lý',
    example: 5,
  })
  @Expose()
  totalProcessed: number;

  @ApiProperty({
    description: 'Số sản phẩm tạo thành công',
    example: 4,
  })
  @Expose()
  successCount: number;

  @ApiProperty({
    description: 'Số sản phẩm tạo thất bại',
    example: 1,
  })
  @Expose()
  errorCount: number;
}
