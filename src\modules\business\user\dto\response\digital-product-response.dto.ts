import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { ProductTypeEnum } from '@modules/business/enums';
import { BaseAdvancedProductResponseDto } from '../base/base-product-response.dto';

/**
 * Response DTO cho digital variant
 */
export class DigitalVariantResponseDto {
  @ApiProperty({
    description: 'ID variant',
    example: 1,
  })
  @Expose()
  id: number;

  @ApiProperty({
    description: 'Tên variant',
    example: 'Basic',
  })
  @Expose()
  name: string;

  @ApiProperty({
    description: 'SKU variant',
    example: 'BASIC-001',
  })
  @Expose()
  sku: string;

  @ApiProperty({
    description: 'Số lượng có sẵn',
    example: 100,
  })
  @Expose()
  availableQuantity: number;

  @ApiProperty({
    description: 'Số lượng tối thiểu mỗi lần mua',
    example: 1,
  })
  @Expose()
  minQuantityPerPurchase: number;

  @ApiProperty({
    description: 'Số lượng tối đa mỗi lần mua',
    example: 5,
  })
  @Expose()
  maxQuantityPerPurchase: number;

  @ApiProperty({
    description: 'Giá variant',
    example: {
      listPrice: 500000,
      salePrice: 400000,
      currency: 'VND'
    },
  })
  @Expose()
  price: any;

  @ApiProperty({
    description: 'Hình ảnh variant',
    type: [Object],
    required: false,
  })
  @Expose()
  images?: any[];

  @ApiProperty({
    description: 'Mô tả variant',
    example: 'Phiên bản cơ bản',
    required: false,
  })
  @Expose()
  description?: string;

  @ApiProperty({
    description: 'Custom fields của variant',
    type: [Object],
    required: false,
  })
  @Expose()
  customFields?: any[];
}

/**
 * Response DTO cho thông tin nâng cao của sản phẩm số
 */
export class DigitalAdvancedInfoResponseDto {
  @ApiProperty({
    description: 'Số lượt mua',
    example: 50,
  })
  @Expose()
  purchaseCount: number;

  @ApiProperty({
    description: 'Danh sách variants',
    type: [DigitalVariantResponseDto],
    required: false,
  })
  @Expose()
  @Type(() => DigitalVariantResponseDto)
  variants?: DigitalVariantResponseDto[];

  @ApiProperty({
    description: 'Hình ảnh nâng cao',
    type: [Object],
    required: false,
  })
  @Expose()
  images?: any[];
}

/**
 * Response DTO cho sản phẩm số (DIGITAL)
 * Kế thừa từ BaseAdvancedProductResponseDto và thêm các trường đặc thù cho sản phẩm số
 */
export class DigitalProductResponseDto extends BaseAdvancedProductResponseDto {
  @ApiProperty({
    description: 'Loại sản phẩm - luôn là DIGITAL',
    enum: ProductTypeEnum,
    example: ProductTypeEnum.DIGITAL,
  })
  @Expose()
  declare productType: ProductTypeEnum.DIGITAL;

  @ApiProperty({
    description: 'Thông tin nâng cao của sản phẩm số',
    type: DigitalAdvancedInfoResponseDto,
    required: false,
  })
  @Expose()
  @Type(() => DigitalAdvancedInfoResponseDto)
  declare advancedInfo?: DigitalAdvancedInfoResponseDto;

  @ApiProperty({
    description: 'Cấu hình vận chuyển (luôn là 0 cho sản phẩm số)',
    example: {
      widthCm: 0,
      heightCm: 0,
      lengthCm: 0,
      weightGram: 0
    },
  })
  @Expose()
  declare shipmentConfig: {
    widthCm: 0;
    heightCm: 0;
    lengthCm: 0;
    weightGram: 0;
  };
}
