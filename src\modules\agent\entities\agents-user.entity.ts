import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, ManyToOne, PrimaryColumn } from 'typeorm';
import { ProfileAgent } from '@modules/agent/interfaces/profile-agent.interface';
import { ConvertConfig } from '@modules/agent/interfaces/convert-config.interface';
import { TypeAgent } from './type-agent.entity';

/**
 * Entity đại diện cho bảng agents_user trong cơ sở dữ liệu
 * Bảng lưu thông tin agent của người dùng, liên kết với users
 */
@Entity('agents_user')
export class AgentUser {
  /**
   * UUID tham chiếu từ agents.id
   */
  @PrimaryColumn('uuid')
  id: string;

  /**
   * ID người dùng sở hữu agent
   */
  @Column({ name: 'user_id', nullable: true })
  userId: number;

  /**
   * ID loại chức năng agent, tham chiếu type_agents
   */
  @Column({ name: 'type_id' })
  typeId: number;

  /**
   * <PERSON><PERSON><PERSON> quan hệ với TypeAgent
   */
  @ManyToOne(() => TypeAgent)
  @JoinColumn({ name: 'type_id' })
  typeAgent: TypeAgent;

  /**
   * UUID tham chiếu đến agent gốc (nếu agent được tạo từ agent khác)
   */
  @Column({ name: 'source_id', type: 'uuid', nullable: true })
  sourceId: string;

  /**
   * Thông tin hồ sơ dạng JSONB (ví dụ: {"bio": "Assistant for tasks"})
   */
  @Column({ type: 'jsonb', default: '{}' })
  profile: ProfileAgent;

  /**
   * Cấu hình chuyển đổi dạng JSONB (ví dụ: [{"name": "customer_name", "type": "string"}])
   */
  @Column({ name: 'convert_config', type: 'jsonb', default: '[]' })
  convertConfig: ConvertConfig[];

  /**
   * Trạng thái hoạt động của agent
   */
  @Column({ name: 'active', type: 'boolean', default: false })
  active: boolean;

  /**
   * Kinh nghiệm của agent
   */
  @Column({ name: 'exp', type: 'bigint', default: 0 })
  exp: number;

  /**
   * UUID tham chiếu đến bảng agents_strategy_user
   */
  @Column({ name: 'strategy_id', type: 'uuid', nullable: true })
  strategyId: string | null;

  /**
   * Trạng thái có thể bán
   */
  @Column({ name: 'is_for_sale', type: 'boolean', default: false })
  isForSale: boolean;

  /**
   * UUID tham chiếu đến bảng user_models
   */
  @Column({ name: 'user_model_id', type: 'uuid', nullable: true })
  userModelId: string | null;

  /**
   * UUID tham chiếu đến bảng system_models
   */
  @Column({ name: 'system_model_id', type: 'uuid', nullable: true })
  systemModelId: string | null;

  /**
   * UUID tham chiếu đến bảng user_model_fine_tune
   */
  @Column({ name: 'model_fine_tune_id', type: 'uuid', nullable: true })
  modelFineTuneId: string | null;

  /**
   * UUID tham chiếu đến bảng key_llm
   */
  @Column({ name: 'key_llm_id', type: 'uuid', nullable: true })
  keyLlmId: string | null;
}
