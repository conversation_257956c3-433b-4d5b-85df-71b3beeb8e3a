import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsNumber,
  IsNotEmpty,
  IsArray,
  ValidateNested,
  Min, IsOptional,
} from 'class-validator';
import { CustomFieldInputDto } from '@modules/business/user/dto';

/**
 * DTO cho Combo Info Item
 */
export class ComboInfoItemDto {
  @ApiProperty({
    description: 'ID sản phẩm',
    example: 123,
  })
  @IsNumber()
  @IsNotEmpty()
  productId: number;

  @ApiProperty({
    description: 'Số lượng sản phẩm trong combo',
    example: 2,
  })
  @IsNumber()
  @Min(1)
  total: number;
}

/**
 * DTO cho Combo Advanced Info
 */
export class ComboAdvancedInfoDto {
  @ApiProperty({
    description: 'Số lượt mua',
    example: 0,
  })
  @IsNumber()
  @Min(0)
  purchaseCount: number;

  @ApiProperty({
    description: 'Thông tin các sản phẩm trong combo',
    type: [ComboInfoItemDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ComboInfoItemDto)
  info: ComboInfoItemDto[];
}
