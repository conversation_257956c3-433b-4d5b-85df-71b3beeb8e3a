import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Put,
  UseGuards,
  Query,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  getSchemaPath,
} from '@nestjs/swagger';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { UserSegmentService } from '../services/user-segment.service';
import {
  CreateSegmentDto,
  UpdateSegmentDto,
  SegmentResponseDto,
  SegmentStatsDto,
  SegmentQueryDto,
  SegmentAudienceResponseDto,
  SegmentAudienceQueryDto,
  SegmentPreviewDto,
  SegmentPreviewResponseDto,
  DeleteMultipleSegmentDto,
  DeleteMultipleSegmentResultDto,
} from '../dto/segment';
import { BulkDeleteSegmentDto, BulkDeleteResponseDto } from '@/modules/marketing/common/dto';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { ApiResponseDto as AppApiResponse, PaginatedResult } from '@/common/response';
import { wrapResponse } from '@/modules/marketing/common/helpers';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { JwtUserGuard } from '@/modules/auth/guards';
import { AvailableFieldsResponseDto } from '@modules/marketing/user/dto';

/**
 * Controller xử lý API liên quan đến segment
 */
@ApiTags(SWAGGER_API_TAGS.USER_SEGMENT)
@ApiBearerAuth("JWT-auth")
@UseGuards(JwtUserGuard)
@Controller('marketing/segments')
export class UserSegmentController {
  constructor(private readonly userSegmentService: UserSegmentService) {}

  /**
   * Tạo segment mới
   */
  @Post()
  @ApiOperation({ summary: 'Tạo segment mới' })
  @ApiResponse({
    status: 201,
    description: 'Segment đã được tạo thành công',
    type: SegmentResponseDto,
  })
  async create(
    @CurrentUser() user: JwtPayload,
    @Body() createSegmentDto: CreateSegmentDto,
  ): Promise<AppApiResponse<SegmentResponseDto>> {
    const result = await this.userSegmentService.create(user.id, createSegmentDto);
    return wrapResponse(result, 'Segment đã được tạo thành công');
  }

  /**
   * Preview segment - tính tổng số audience phù hợp với điều kiện
   * Nếu cung cấp segmentId thì sẽ cập nhật số lượng audience vào segment đó
   */
  @Post('preview')
  @ApiOperation({ 
    summary: 'Preview segment - tính tổng số audience phù hợp với điều kiện', 
    description: 'Tính tổng số audience phù hợp với điều kiện. Nếu cung cấp segmentId thì sẽ cập nhật số lượng audience vào segment đó'
  })
  @ApiResponse({
    status: 200,
    description: 'Thống kê số audience phù hợp với điều kiện segment',
    type: SegmentPreviewResponseDto,
  })
  async previewSegment(
    @CurrentUser() user: JwtPayload,
    @Body() previewDto: SegmentPreviewDto,
  ): Promise<AppApiResponse<SegmentPreviewResponseDto>> {
    const result = await this.userSegmentService.previewSegmentAudienceCount(user.id, previewDto);
    return wrapResponse(result, 'Thống kê audience phù hợp với điều kiện segment');
  }

  /**
   * Lấy danh sách các trường có thể sử dụng để tạo segment criteria
   */
  @Get('available-fields')
  @ApiOperation({
    summary: 'Lấy danh sách các trường có thể sử dụng để tạo segment criteria',
    description: 'Trả về danh sách các trường built-in và custom fields mà người dùng có thể sử dụng để tạo điều kiện phân đoạn audience. Bao gồm thông tin về loại dữ liệu và các toán tử có thể sử dụng.'
  })
  @ApiResponse({
    status: 200,
    description: 'Danh sách các trường có thể sử dụng cho segment criteria',
    schema: {
      allOf: [
        { $ref: getSchemaPath(AppApiResponse) },
        {
          properties: {
            result: { $ref: getSchemaPath(AvailableFieldsResponseDto) }
          }
        }
      ]
    }
  })
  async getAvailableFields(
    @CurrentUser() user: JwtPayload,
  ): Promise<AppApiResponse<AvailableFieldsResponseDto>> {
    const result = await this.userSegmentService.getAvailableFields(user.id);
    return wrapResponse(result, 'Lấy danh sách trường có thể sử dụng thành công');
  }

  /**
   * Lấy danh sách segment với phân trang và tìm kiếm
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách segment với phân trang và tìm kiếm' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách segment với phân trang',
    schema: {
      allOf: [
        { $ref: getSchemaPath(AppApiResponse) },
        {
          properties: {
            result: {
              allOf: [
                { $ref: getSchemaPath(PaginatedResult) },
                {
                  properties: {
                    items: {
                      type: 'array',
                      items: { $ref: getSchemaPath(SegmentResponseDto) }
                    }
                  }
                }
              ]
            }
          }
        }
      ]
    }
  })
  async findAll(
    @CurrentUser() user: JwtPayload,
    @Query() queryDto: SegmentQueryDto,
  ): Promise<AppApiResponse<PaginatedResult<SegmentResponseDto>>> {
    const result = await this.userSegmentService.findAll(user.id, queryDto);
    return wrapResponse(result, 'Danh sách segment');
  }

  /**
   * Lấy thông tin segment theo ID
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy thông tin segment theo ID' })
  @ApiResponse({
    status: 200,
    description: 'Thông tin segment',
    type: SegmentResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Segment không tồn tại' })
  async findOne(
    @CurrentUser() user: JwtPayload,
    @Param('id') id: string,
  ): Promise<AppApiResponse<SegmentResponseDto>> {
    const result = await this.userSegmentService.findOne(user.id, +id);
    return wrapResponse(result, 'Thông tin segment');
  }

  /**
   * Lấy thống kê của segment
   */
  @Get(':id/stats')
  @ApiOperation({ summary: 'Lấy thống kê của segment' })
  @ApiResponse({
    status: 200,
    description: 'Thống kê segment',
    type: SegmentStatsDto,
  })
  @ApiResponse({ status: 404, description: 'Segment không tồn tại' })
  async getStats(
    @CurrentUser() user: JwtPayload,
    @Param('id') id: string,
  ): Promise<AppApiResponse<SegmentStatsDto>> {
    const result = await this.userSegmentService.getStats(user.id, +id);
    return wrapResponse(result, 'Thống kê segment');
  }

  /**
   * Lấy danh sách audience trong segment
   */
  @Get(':id/audiences')
  @ApiOperation({ summary: 'Lấy danh sách audience trong segment' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách audience trong segment với phân trang',
    schema: {
      allOf: [
        { $ref: getSchemaPath(AppApiResponse) },
        {
          properties: {
            result: {
              allOf: [
                { $ref: getSchemaPath(PaginatedResult) },
                {
                  properties: {
                    items: {
                      type: 'array',
                      items: { $ref: getSchemaPath(SegmentAudienceResponseDto) }
                    }
                  }
                }
              ]
            }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 404, description: 'Segment không tồn tại' })
  async getAudiences(
    @CurrentUser() user: JwtPayload,
    @Param('id') id: string,
    @Query() queryDto: SegmentAudienceQueryDto,
  ): Promise<AppApiResponse<PaginatedResult<SegmentAudienceResponseDto>>> {
    const result = await this.userSegmentService.getSegmentAudiences(user.id, +id, queryDto);
    return wrapResponse(result, 'Danh sách audience trong segment');
  }

  /**
   * Cập nhật segment
   */
  @Put(':id')
  @ApiOperation({ summary: 'Cập nhật segment' })
  @ApiResponse({
    status: 200,
    description: 'Segment đã được cập nhật thành công',
    type: SegmentResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Segment không tồn tại' })
  async update(
    @CurrentUser() user: JwtPayload,
    @Param('id') id: string,
    @Body() updateSegmentDto: UpdateSegmentDto,
  ): Promise<AppApiResponse<SegmentResponseDto>> {
    const result = await this.userSegmentService.update(user.id, +id, updateSegmentDto);
    return wrapResponse(result, 'Segment đã được cập nhật thành công');
  }

  /**
   * Xóa nhiều segment
   */
  @Delete('bulk')
  @ApiOperation({
    summary: 'Xóa nhiều segment',
    description: 'Xóa nhiều segment cùng lúc. API sẽ trả về danh sách các segment đã xóa thành công và thất bại với lý do cụ thể.'
  })
  @ApiResponse({
    status: 200,
    description: 'Kết quả xóa nhiều segment',
    schema: {
      allOf: [
        { $ref: getSchemaPath(AppApiResponse) },
        {
          properties: {
            result: { $ref: getSchemaPath(DeleteMultipleSegmentResultDto) }
          }
        }
      ]
    }
  })
  @ApiResponse({
    status: 400,
    description: 'Dữ liệu không hợp lệ',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 400 },
        message: { type: 'string', example: 'Dữ liệu không hợp lệ' },
        errors: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              field: { type: 'string', example: 'ids' },
              message: { type: 'string', example: 'Phải có ít nhất 1 ID để xóa' }
            }
          }
        }
      }
    }
  })
  async removeMultiple(
    @CurrentUser() user: JwtPayload,
    @Body() deleteDto: DeleteMultipleSegmentDto,
  ): Promise<AppApiResponse<DeleteMultipleSegmentResultDto>> {
    const result = await this.userSegmentService.removeMultiple(user.id, deleteDto.ids);

    let message = 'Xóa segment thành công';
    if (result.totalFailed > 0) {
      message = `Đã xóa ${result.totalDeleted} segment thành công, ${result.totalFailed} segment thất bại`;
    } else {
      message = `Đã xóa ${result.totalDeleted} segment thành công`;
    }

    return wrapResponse(result, message);
  }

  /**
   * Cập nhật số lượng audience của segment
   */
  @Post(':id/calculate-audience-count')
  @ApiOperation({ summary: 'Tính và cập nhật số lượng audience trong segment' })
  @ApiResponse({
    status: 200,
    description: 'Segment đã được cập nhật với số lượng audience',
    schema: {
      allOf: [
        { $ref: getSchemaPath(AppApiResponse) },
        {
          properties: {
            result: { $ref: getSchemaPath(SegmentResponseDto) },
          },
        },
      ],
    },
  })
  async calculateAudienceCount(
    @CurrentUser() user: JwtPayload,
    @Param('id') id: number,
  ): Promise<AppApiResponse<SegmentResponseDto>> {
    const result = await this.userSegmentService.calculateAndUpdateAudienceCount(user.id, id);
    return wrapResponse(result, 'Số lượng audience trong segment đã được cập nhật');
  }

  /**
   * Xóa nhiều segment
   */
  @Delete()
  @ApiOperation({
    summary: 'Xóa nhiều segment',
    description: 'Xóa nhiều segment cùng lúc. API sẽ trả về thông tin chi tiết về các segment đã xóa thành công và thất bại.'
  })
  @ApiResponse({
    status: 200,
    description: 'Kết quả xóa nhiều segment',
    schema: {
      allOf: [
        { $ref: getSchemaPath(AppApiResponse) },
        {
          properties: {
            result: { $ref: getSchemaPath(BulkDeleteResponseDto) }
          }
        }
      ]
    }
  })
  @ApiResponse({
    status: 400,
    description: 'Dữ liệu không hợp lệ',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 400 },
        message: { type: 'string', example: 'Dữ liệu không hợp lệ' },
        errors: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              field: { type: 'string', example: 'ids' },
              message: { type: 'string', example: 'Phải có ít nhất 1 segment để xóa' }
            }
          }
        }
      }
    }
  })
  async bulkDelete(
    @CurrentUser() user: JwtPayload,
    @Body() bulkDeleteDto: BulkDeleteSegmentDto,
  ): Promise<AppApiResponse<BulkDeleteResponseDto>> {
    const result = await this.userSegmentService.bulkDelete(user.id, bulkDeleteDto.ids);
    return wrapResponse(result, 'Xóa nhiều segment hoàn tất');
  }
}
