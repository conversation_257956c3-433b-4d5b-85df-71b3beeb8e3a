import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { ProductTypeEnum } from '@modules/business/enums';
import { BaseAdvancedProductResponseDto } from '../base/base-product-response.dto';

/**
 * Enum cho hình thức tổ chức sự kiện
 */
export enum EventFormatEnum {
  ONLINE = 'ONLINE',
  OFFLINE = 'OFFLINE',
  HYBRID = 'HYBRID'
}

/**
 * Response DTO cho ticket type
 */
export class TicketTypeResponseDto {
  @ApiProperty({
    description: 'ID ticket type',
    example: 1,
  })
  @Expose()
  id: number;

  @ApiProperty({
    description: 'Tên loại vé',
    example: 'Vé thường',
  })
  @Expose()
  name: string;

  @ApiProperty({
    description: 'Giá vé',
    example: 200000,
  })
  @Expose()
  price: number;

  @ApiProperty({
    description: 'Thời gian bắt đầu bán vé (timestamp)',
    example: 1704067200000,
  })
  @Expose()
  startTime: number;

  @ApiProperty({
    description: 'Thời gian kết thúc bán vé (timestamp)',
    example: 1704153600000,
  })
  @Expose()
  endTime: number;

  @ApiProperty({
    description: 'Múi giờ',
    example: 'Asia/Ho_Chi_Minh',
  })
  @Expose()
  timezone: string;

  @ApiProperty({
    description: 'Mô tả loại vé',
    example: 'Vé tham gia sự kiện cơ bản',
    required: false,
  })
  @Expose()
  description?: string;

  @ApiProperty({
    description: 'Số lượng vé',
    example: 100,
  })
  @Expose()
  quantity: number;

  @ApiProperty({
    description: 'Số lượng tối thiểu mỗi lần mua',
    example: 1,
  })
  @Expose()
  minQuantityPerPurchase: number;

  @ApiProperty({
    description: 'Số lượng tối đa mỗi lần mua',
    example: 5,
  })
  @Expose()
  maxQuantityPerPurchase: number;

  @ApiProperty({
    description: 'Trạng thái ticket type',
    example: 'PENDING',
  })
  @Expose()
  status: string;

  @ApiProperty({
    description: 'Hình ảnh ticket type',
    type: [Object],
    required: false,
  })
  @Expose()
  images?: any[];
}

/**
 * Response DTO cho thông tin nâng cao của sự kiện
 */
export class EventAdvancedInfoResponseDto {
  @ApiProperty({
    description: 'Số lượt mua',
    example: 75,
  })
  @Expose()
  purchaseCount: number;

  @ApiProperty({
    description: 'Hình thức tổ chức sự kiện',
    enum: EventFormatEnum,
    example: EventFormatEnum.OFFLINE,
  })
  @Expose()
  eventFormat: EventFormatEnum;

  @ApiProperty({
    description: 'Đường dẫn tham gia sự kiện (cho sự kiện online)',
    example: 'https://zoom.us/j/123456789',
    required: false,
  })
  @Expose()
  eventLink?: string;

  @ApiProperty({
    description: 'Địa điểm tham gia sự kiện (cho sự kiện offline)',
    example: 'Trung tâm Hội nghị Quốc gia, Hà Nội',
    required: false,
  })
  @Expose()
  eventLocation?: string;

  @ApiProperty({
    description: 'Ngày bắt đầu sự kiện (timestamp)',
    example: 1704067200000,
  })
  @Expose()
  startDate: number;

  @ApiProperty({
    description: 'Ngày kết thúc sự kiện (timestamp)',
    example: 1704153600000,
  })
  @Expose()
  endDate: number;

  @ApiProperty({
    description: 'Múi giờ',
    example: 'Asia/Ho_Chi_Minh',
  })
  @Expose()
  timezone: string;

  @ApiProperty({
    description: 'Danh sách loại vé sự kiện',
    type: [TicketTypeResponseDto],
    required: false,
  })
  @Expose()
  @Type(() => TicketTypeResponseDto)
  ticketTypes?: TicketTypeResponseDto[];

  @ApiProperty({
    description: 'Hình ảnh nâng cao',
    type: [Object],
    required: false,
  })
  @Expose()
  images?: any[];
}

/**
 * Response DTO cho sản phẩm sự kiện (EVENT)
 * Kế thừa từ BaseAdvancedProductResponseDto và thêm các trường đặc thù cho sự kiện
 */
export class EventProductResponseDto extends BaseAdvancedProductResponseDto {
  @ApiProperty({
    description: 'Loại sản phẩm - luôn là EVENT',
    enum: ProductTypeEnum,
    example: ProductTypeEnum.EVENT,
  })
  @Expose()
  declare productType: ProductTypeEnum.EVENT;

  @ApiProperty({
    description: 'Thông tin nâng cao của sản phẩm sự kiện',
    type: EventAdvancedInfoResponseDto,
    required: false,
  })
  @Expose()
  @Type(() => EventAdvancedInfoResponseDto)
  declare advancedInfo?: EventAdvancedInfoResponseDto;

  @ApiProperty({
    description: 'Cấu hình vận chuyển (luôn là 0 cho sản phẩm sự kiện)',
    example: {
      widthCm: 0,
      heightCm: 0,
      lengthCm: 0,
      weightGram: 0
    },
  })
  @Expose()
  declare shipmentConfig: {
    widthCm: 0;
    heightCm: 0;
    lengthCm: 0;
    weightGram: 0;
  };
}
