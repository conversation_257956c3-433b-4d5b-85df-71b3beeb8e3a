import { ErrorCode } from '@common/exceptions';
import { HttpStatus } from '@nestjs/common';

/**
 * Mã lỗi cho module Integration
 * Phạm vi: 11000-11999
 */
export const INTEGRATION_ERROR_CODES = {
  // Email Server Configuration (11000-11099)
  EMAIL_SERVER_NOT_FOUND: new ErrorCode(11000, 'Không tìm thấy cấu hình máy chủ email', HttpStatus.NOT_FOUND),
  EMAIL_SERVER_LIST_FAILED: new ErrorCode(11001, 'Không thể lấy danh sách cấu hình máy chủ email', HttpStatus.INTERNAL_SERVER_ERROR),
  EMAIL_SERVER_DETAIL_FAILED: new ErrorCode(11002, 'Không thể lấy thông tin chi tiết cấu hình máy chủ email', HttpStatus.INTERNAL_SERVER_ERROR),
  EMAIL_SERVER_CREATE_FAILED: new ErrorCode(11003, 'Không thể tạo cấu hình máy chủ email', HttpStatus.BAD_REQUEST),
  EMAIL_SERVER_UPDATE_FAILED: new ErrorCode(11004, 'Không thể cập nhật cấu hình máy chủ email', HttpStatus.BAD_REQUEST),
  EMAIL_SERVER_DELETE_FAILED: new ErrorCode(11005, 'Không thể xóa cấu hình máy chủ email', HttpStatus.INTERNAL_SERVER_ERROR),
  EMAIL_SERVER_TEST_FAILED: new ErrorCode(11006, 'Không thể kiểm tra kết nối máy chủ email', HttpStatus.BAD_REQUEST),

  // User Key (11100-11199)
  USER_KEY_NOT_FOUND: new ErrorCode(11100, 'Không tìm thấy API key', HttpStatus.NOT_FOUND),
  USER_KEY_LIST_FAILED: new ErrorCode(11101, 'Không thể lấy danh sách API key', HttpStatus.INTERNAL_SERVER_ERROR),
  USER_KEY_DETAIL_FAILED: new ErrorCode(11102, 'Không thể lấy thông tin chi tiết API key', HttpStatus.INTERNAL_SERVER_ERROR),
  USER_KEY_CREATE_FAILED: new ErrorCode(11103, 'Không thể tạo API key', HttpStatus.BAD_REQUEST),
  USER_KEY_UPDATE_FAILED: new ErrorCode(11104, 'Không thể cập nhật API key', HttpStatus.BAD_REQUEST),
  USER_KEY_DELETE_FAILED: new ErrorCode(11105, 'Không thể xóa API key', HttpStatus.INTERNAL_SERVER_ERROR),
  USER_KEY_TEST_FAILED: new ErrorCode(11106, 'Không thể kiểm tra API key', HttpStatus.BAD_REQUEST),

  // Payment Gateway (11200-11299)
  PAYMENT_GATEWAY_NOT_FOUND: new ErrorCode(11200, 'Không tìm thấy cổng thanh toán', HttpStatus.NOT_FOUND),
  PAYMENT_GATEWAY_LIST_FAILED: new ErrorCode(11201, 'Không thể lấy danh sách cổng thanh toán', HttpStatus.INTERNAL_SERVER_ERROR),
  PAYMENT_GATEWAY_DETAIL_FAILED: new ErrorCode(11202, 'Không thể lấy thông tin chi tiết cổng thanh toán', HttpStatus.INTERNAL_SERVER_ERROR),
  PAYMENT_GATEWAY_CREATE_FAILED: new ErrorCode(11203, 'Không thể tạo cổng thanh toán', HttpStatus.BAD_REQUEST),
  PAYMENT_GATEWAY_UPDATE_FAILED: new ErrorCode(11204, 'Không thể cập nhật cổng thanh toán', HttpStatus.BAD_REQUEST),
  PAYMENT_GATEWAY_DELETE_FAILED: new ErrorCode(11205, 'Không thể xóa cổng thanh toán', HttpStatus.INTERNAL_SERVER_ERROR),

  // Facebook Page (11300-11399)
  FACEBOOK_PAGE_NOT_FOUND: new ErrorCode(11300, 'Không tìm thấy trang Facebook', HttpStatus.NOT_FOUND),
  FACEBOOK_PAGE_LIST_FAILED: new ErrorCode(11301, 'Không thể lấy danh sách trang Facebook', HttpStatus.INTERNAL_SERVER_ERROR),
  FACEBOOK_PAGE_DETAIL_FAILED: new ErrorCode(11302, 'Không thể lấy thông tin chi tiết trang Facebook', HttpStatus.INTERNAL_SERVER_ERROR),
  FACEBOOK_PAGE_CREATE_FAILED: new ErrorCode(11303, 'Không thể tạo trang Facebook', HttpStatus.BAD_REQUEST),
  FACEBOOK_PAGE_UPDATE_FAILED: new ErrorCode(11304, 'Không thể cập nhật trang Facebook', HttpStatus.BAD_REQUEST),
  FACEBOOK_PAGE_DELETE_FAILED: new ErrorCode(11305, 'Không thể xóa trang Facebook', HttpStatus.INTERNAL_SERVER_ERROR),
  FACEBOOK_PAGE_UNSUBSCRIBE_FAILED: new ErrorCode(11306, 'Không thể hủy đăng ký webhook cho trang Facebook', HttpStatus.INTERNAL_SERVER_ERROR),
  FACEBOOK_PAGE_ALREADY_INTEGRATED: new ErrorCode(11307, 'Trang Facebook đã được tích hợp với agent khác', HttpStatus.CONFLICT),
  FACEBOOK_PAGE_NOT_INTEGRATED: new ErrorCode(11308, 'Trang Facebook chưa được tích hợp với agent', HttpStatus.BAD_REQUEST),
  FACEBOOK_PAGE_INTEGRATION_FAILED: new ErrorCode(11309, 'Lỗi khi tích hợp trang Facebook với agent', HttpStatus.INTERNAL_SERVER_ERROR),
  FACEBOOK_PAGE_SUBSCRIBE_FAILED: new ErrorCode(11310, 'Không thể đăng ký webhook cho trang Facebook', HttpStatus.INTERNAL_SERVER_ERROR),
  FACEBOOK_PAGE_REMOVE_FAILED: new ErrorCode(11311, 'Lỗi khi gỡ trang Facebook khỏi agent', HttpStatus.INTERNAL_SERVER_ERROR),
  FACEBOOK_PAGE_AUTH_INVALID: new ErrorCode(11312, 'State không hợp lệ', HttpStatus.BAD_REQUEST),
  FACEBOOK_PAGE_AUTH_CODE_USED: new ErrorCode(11313, 'Mã xác thực Facebook đã được sử dụng', HttpStatus.BAD_REQUEST),
  FACEBOOK_PAGE_AUTH_CODE_EXPIRED: new ErrorCode(11314, 'Mã xác thực Facebook đã hết hạn', HttpStatus.BAD_REQUEST),

  // SMS Server Configuration (11400-11499)
  SMS_SERVER_NOT_FOUND: new ErrorCode(11400, 'Không tìm thấy cấu hình máy chủ SMS', HttpStatus.NOT_FOUND),
  SMS_SERVER_LIST_FAILED: new ErrorCode(11401, 'Không thể lấy danh sách cấu hình máy chủ SMS', HttpStatus.INTERNAL_SERVER_ERROR),
  SMS_SERVER_DETAIL_FAILED: new ErrorCode(11402, 'Không thể lấy thông tin chi tiết cấu hình máy chủ SMS', HttpStatus.INTERNAL_SERVER_ERROR),
  SMS_SERVER_CREATE_FAILED: new ErrorCode(11403, 'Không thể tạo cấu hình máy chủ SMS', HttpStatus.BAD_REQUEST),
  SMS_SERVER_UPDATE_FAILED: new ErrorCode(11404, 'Không thể cập nhật cấu hình máy chủ SMS', HttpStatus.BAD_REQUEST),
  SMS_SERVER_DELETE_FAILED: new ErrorCode(11405, 'Không thể xóa cấu hình máy chủ SMS', HttpStatus.INTERNAL_SERVER_ERROR),
  SMS_SERVER_TEST_FAILED: new ErrorCode(11406, 'Không thể kiểm tra kết nối máy chủ SMS', HttpStatus.BAD_REQUEST),

  // WhatsApp Integration (11500-11599)
  WHATSAPP_ACCOUNT_NOT_FOUND: new ErrorCode(11500, 'Không tìm thấy tài khoản WhatsApp', HttpStatus.NOT_FOUND),
  WHATSAPP_ACCOUNT_LIST_ERROR: new ErrorCode(11501, 'Lỗi khi lấy danh sách tài khoản WhatsApp', HttpStatus.INTERNAL_SERVER_ERROR),
  WHATSAPP_ACCOUNT_FIND_ERROR: new ErrorCode(11502, 'Lỗi khi lấy thông tin tài khoản WhatsApp', HttpStatus.INTERNAL_SERVER_ERROR),
  WHATSAPP_ACCOUNT_CREATE_ERROR: new ErrorCode(11503, 'Lỗi khi tạo tài khoản WhatsApp', HttpStatus.BAD_REQUEST),
  WHATSAPP_ACCOUNT_UPDATE_ERROR: new ErrorCode(11504, 'Lỗi khi cập nhật tài khoản WhatsApp', HttpStatus.BAD_REQUEST),
  WHATSAPP_ACCOUNT_DELETE_ERROR: new ErrorCode(11505, 'Lỗi khi xóa tài khoản WhatsApp', HttpStatus.INTERNAL_SERVER_ERROR),
  WHATSAPP_ACCOUNT_ALREADY_EXISTS: new ErrorCode(11506, 'Tài khoản WhatsApp đã tồn tại', HttpStatus.CONFLICT),
  WHATSAPP_ACCOUNT_ACCESS_DENIED: new ErrorCode(11507, 'Không có quyền truy cập tài khoản WhatsApp này', HttpStatus.FORBIDDEN),
  WHATSAPP_ACCOUNT_CONNECT_ERROR: new ErrorCode(11508, 'Lỗi khi kết nối tài khoản WhatsApp với agent', HttpStatus.INTERNAL_SERVER_ERROR),
  WHATSAPP_ACCOUNT_DISCONNECT_ERROR: new ErrorCode(11509, 'Lỗi khi ngắt kết nối tài khoản WhatsApp với agent', HttpStatus.INTERNAL_SERVER_ERROR),
  WHATSAPP_TEMPLATE_NOT_FOUND: new ErrorCode(11510, 'Không tìm thấy mẫu tin nhắn WhatsApp', HttpStatus.NOT_FOUND),
  WHATSAPP_TEMPLATE_CREATE_ERROR: new ErrorCode(11511, 'Lỗi khi tạo mẫu tin nhắn WhatsApp', HttpStatus.BAD_REQUEST),
  WHATSAPP_TEMPLATE_UPDATE_ERROR: new ErrorCode(11512, 'Lỗi khi cập nhật mẫu tin nhắn WhatsApp', HttpStatus.BAD_REQUEST),
  WHATSAPP_TEMPLATE_DELETE_ERROR: new ErrorCode(11513, 'Lỗi khi xóa mẫu tin nhắn WhatsApp', HttpStatus.INTERNAL_SERVER_ERROR),
  WHATSAPP_MESSAGE_SEND_ERROR: new ErrorCode(11514, 'Lỗi khi gửi tin nhắn WhatsApp', HttpStatus.INTERNAL_SERVER_ERROR),
  WHATSAPP_MESSAGE_INVALID: new ErrorCode(11515, 'Tin nhắn WhatsApp không hợp lệ', HttpStatus.BAD_REQUEST),
  WHATSAPP_WEBHOOK_ERROR: new ErrorCode(11516, 'Lỗi khi xử lý webhook WhatsApp', HttpStatus.INTERNAL_SERVER_ERROR),

  // Website Related Errors (11800-11899)
  WEBSITE_NOT_FOUND: new ErrorCode(11800, 'Không tìm thấy website', HttpStatus.NOT_FOUND),
  WEBSITE_DELETE_FAILED: new ErrorCode(11801, 'Lỗi khi xóa website', HttpStatus.INTERNAL_SERVER_ERROR),
  WEBSITE_ACCESS_DENIED: new ErrorCode(11802, 'Không có quyền truy cập website này', HttpStatus.FORBIDDEN),

  // Integration CRUD (11600-11699)
  INTEGRATION_NOT_FOUND: new ErrorCode(11600, 'Không tìm thấy tích hợp', HttpStatus.NOT_FOUND),
  INTEGRATION_CREATE_FAILED: new ErrorCode(11601, 'Không thể tạo tích hợp', HttpStatus.BAD_REQUEST),
  INTEGRATION_UPDATE_FAILED: new ErrorCode(11602, 'Không thể cập nhật tích hợp', HttpStatus.BAD_REQUEST),
  INTEGRATION_DELETE_FAILED: new ErrorCode(11603, 'Không thể xóa tích hợp', HttpStatus.INTERNAL_SERVER_ERROR),
  INTEGRATION_LIST_FAILED: new ErrorCode(11604, 'Không thể lấy danh sách tích hợp', HttpStatus.INTERNAL_SERVER_ERROR),
  INTEGRATION_ACCESS_DENIED: new ErrorCode(11605, 'Không có quyền truy cập tích hợp này', HttpStatus.FORBIDDEN),

  // Agent Related Errors
  AGENT_NOT_FOUND: new ErrorCode(11900, 'Không tìm thấy agent', HttpStatus.NOT_FOUND),
  AGENT_ACCESS_DENIED: new ErrorCode(11901, 'Không có quyền truy cập agent này', HttpStatus.FORBIDDEN),
};
