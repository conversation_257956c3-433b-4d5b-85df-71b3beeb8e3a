import { ProductTypeEnum } from '@modules/business/enums';
import {} from '@modules/business/user/dto';
import { PhysicalProductCreateDto } from './create/physical-product.dto';
import { DigitalProductCreateDto } from './create/digital-product-create.dto';
import { EventProductCreateDto } from './create/event-product-create.dto';
import { ServiceProductCreateDto } from './create/service-product-create.dto';
import { ComboProductCreateDto } from './create/combo-product-create.dto';

/**
 * Union type cho tất cả các loại sản phẩm create DTOs
 * Sử dụng để type-safe trong các handler và service
 */
export type CreatedProductDto =
  | PhysicalProductCreateDto
  | DigitalProductCreateDto
  | EventProductCreateDto
  | ServiceProductCreateDto
  | ComboProductCreateDto;

/**
 * Type guard functions để kiểm tra loại sản phẩm
 */
export function isPhysicalProductDto(
  dto: CreatedProductDto,
): dto is PhysicalProductCreateDto {
  return dto.productType === ProductTypeEnum.PHYSICAL;
}

export function isDigitalProductDto(
  dto: CreatedProductDto,
): dto is DigitalProductCreateDto {
  return dto.productType === ProductTypeEnum.DIGITAL;
}

export function isEventProductDto(
  dto: CreatedProductDto,
): dto is EventProductCreateDto {
  return dto.productType === ProductTypeEnum.EVENT;
}

export function isServiceProductDto(
  dto: CreatedProductDto,
): dto is ServiceProductCreateDto {
  return dto.productType === ProductTypeEnum.SERVICE;
}

export function isComboProductDto(
  dto: CreatedProductDto,
): dto is ComboProductCreateDto {
  return dto.productType === ProductTypeEnum.COMBO;
}
