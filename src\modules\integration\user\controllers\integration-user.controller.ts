import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiExtraModels,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards';
import { CurrentUser } from '@modules/auth/decorators';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { ApiErrorResponseDto } from '@common/dto/api-error-response.dto';
import { INTEGRATION_ERROR_CODES } from '@modules/integration/exceptions/integration-error.code';
import { IntegrationUserService } from '../services';
import {
  CreateIntegrationDto,
  UpdateIntegrationDto,
  IntegrationResponseDto,
  QueryIntegrationDto,
} from '../dto/integration';
import { SWAGGER_API_TAGS } from '@common/swagger';

/**
 * Controller xử lý các API liên quan đến tích hợp của người dùng
 */
@ApiTags(SWAGGER_API_TAGS.USER_INTEGRATION)
@ApiExtraModels(ApiResponseDto, IntegrationResponseDto, PaginatedResult, ApiErrorResponseDto)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtUserGuard)
@Controller('user/integration')
export class IntegrationUserController {
  constructor(private readonly integrationUserService: IntegrationUserService) {}

  /**
   * Lấy danh sách tích hợp
   * @param queryDto Tham số truy vấn
   * @param user Thông tin người dùng
   * @returns Danh sách tích hợp với phân trang
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách tích hợp' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách tích hợp',
    schema: ApiResponseDto.getPaginatedSchema(IntegrationResponseDto),
  })
  @ApiErrorResponse(INTEGRATION_ERROR_CODES.INTEGRATION_LIST_FAILED)
  async getIntegrations(
    @Query() queryDto: QueryIntegrationDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<PaginatedResult<IntegrationResponseDto>>> {
    const result = await this.integrationUserService.getIntegrations(queryDto, user.id);
    return ApiResponseDto.success(result, 'Lấy danh sách tích hợp thành công');
  }

  /**
   * Lấy thông tin tích hợp theo ID
   * @param id ID của tích hợp
   * @param user Thông tin người dùng
   * @returns Thông tin chi tiết của tích hợp
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy thông tin tích hợp theo ID' })
  @ApiParam({ name: 'id', description: 'ID của tích hợp', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Thông tin tích hợp',
    schema: ApiResponseDto.getSchema(IntegrationResponseDto),
  })
  @ApiErrorResponse(INTEGRATION_ERROR_CODES.INTEGRATION_NOT_FOUND, INTEGRATION_ERROR_CODES.INTEGRATION_LIST_FAILED)
  async getIntegrationById(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<IntegrationResponseDto>> {
    const result = await this.integrationUserService.getIntegrationById(id, user.id);
    return ApiResponseDto.success(result, 'Lấy thông tin tích hợp thành công');
  }

  /**
   * Tạo mới tích hợp
   * @param createDto DTO chứa thông tin tạo mới
   * @param user Thông tin người dùng
   * @returns Thông tin tích hợp đã tạo
   */
  @Post()
  @ApiOperation({ summary: 'Tạo mới tích hợp' })
  @ApiBody({ type: CreateIntegrationDto })
  @ApiResponse({
    status: 201,
    description: 'Tạo mới tích hợp thành công',
    schema: ApiResponseDto.getSchema(IntegrationResponseDto),
  })
  @ApiErrorResponse(INTEGRATION_ERROR_CODES.INTEGRATION_CREATE_FAILED)
  async createIntegration(
    @Body() createDto: CreateIntegrationDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<IntegrationResponseDto>> {
    const result = await this.integrationUserService.createIntegration(createDto, user.id);
    return ApiResponseDto.created(result, 'Tạo mới tích hợp thành công');
  }

  /**
   * Cập nhật tích hợp
   * @param id ID của tích hợp
   * @param updateDto DTO chứa thông tin cập nhật
   * @param user Thông tin người dùng
   * @returns Thông tin tích hợp đã cập nhật
   */
  @Put(':id')
  @ApiOperation({ summary: 'Cập nhật tích hợp' })
  @ApiParam({ name: 'id', description: 'ID của tích hợp', type: 'number' })
  @ApiBody({ type: UpdateIntegrationDto })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật tích hợp thành công',
    schema: ApiResponseDto.getSchema(IntegrationResponseDto),
  })
  @ApiErrorResponse(INTEGRATION_ERROR_CODES.INTEGRATION_NOT_FOUND, INTEGRATION_ERROR_CODES.INTEGRATION_UPDATE_FAILED)
  async updateIntegration(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateIntegrationDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<IntegrationResponseDto>> {
    const result = await this.integrationUserService.updateIntegration(id, updateDto, user.id);
    return ApiResponseDto.success(result, 'Cập nhật tích hợp thành công');
  }

  /**
   * Xóa tích hợp
   * @param id ID của tích hợp
   * @param user Thông tin người dùng
   * @returns Kết quả xóa
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa tích hợp' })
  @ApiParam({ name: 'id', description: 'ID của tích hợp', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Xóa tích hợp thành công',
    schema: ApiResponseDto.getSchema(null),
  })
  @ApiErrorResponse(INTEGRATION_ERROR_CODES.INTEGRATION_NOT_FOUND, INTEGRATION_ERROR_CODES.INTEGRATION_DELETE_FAILED)
  async deleteIntegration(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<null>> {
    await this.integrationUserService.deleteIntegration(id, user.id);
    return ApiResponseDto.success(null, 'Xóa tích hợp thành công');
  }
} 