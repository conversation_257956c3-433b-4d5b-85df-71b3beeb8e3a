/**
 * Enum cho các loại trường tùy chỉnh được hỗ trợ
 * Chỉ bao gồm các loại trường đã được implement validation
 */
export enum CustomFieldTypeEnum {
  // Các loại trường cơ bản
  TEXT = 'text',
  EMAIL = 'email',
  PASSWORD = 'password',
  NUMBER = 'number',
  TEL = 'tel',

  // Các loại trường thời gian
  DATE = 'date',
  DATETIME = 'datetime',
  TIME = 'time',

  // Các loại trường lựa chọn
  SELECT = 'select',
  CHECKBOX = 'checkbox',
  RADIO = 'radio',

  // Các loại trường văn bản mở rộng
  TEXTAREA = 'textarea',

  // Các loại trường dữ liệu đơn giản
  BOOLEAN = 'boolean',

  // Các loại trường dữ liệu phức tạp (được hỗ trợ bởi frontend)
  OBJECT = 'object',
  ARRAY = 'array',
}

/**
 * Danh sách tất cả các loại trường được hỗ trợ
 */
export const SUPPORTED_CUSTOM_FIELD_TYPES = Object.values(CustomFieldTypeEnum);

/**
 * Kiểm tra xem loại trường có được hỗ trợ hay không
 * @param type Loại trường cần kiểm tra
 * @returns true nếu được hỗ trợ, false nếu không
 */
export function isValidCustomFieldType(type: string): boolean {
  return SUPPORTED_CUSTOM_FIELD_TYPES.includes(type as CustomFieldTypeEnum);
}

/**
 * Lấy danh sách các loại trường dưới dạng chuỗi
 * @returns Mảng các loại trường
 */
export function getCustomFieldTypesList(): string[] {
  return SUPPORTED_CUSTOM_FIELD_TYPES;
}
