# Test API Agent Website với <PERSON>ân Trang

## API Endpoint
```
GET /user/agents/{agentId}/websites
```

## Query Parameters
- `page` (number, optional): Trang hiện tại (default: 1)
- `limit` (number, optional): S<PERSON> lượng item trên mỗi trang (default: 10, max: 100)
- `search` (string, optional): Từ khóa tìm kiếm theo tên website hoặc host
- `verify` (boolean, optional): Lọ<PERSON> theo trạng thái xác minh
- `sortBy` (enum, optional): Tr<PERSON><PERSON><PERSON> sắp xếp (websiteName, host, createdAt, verify) - default: createdAt
- `sortDirection` (enum, optional): Hướng sắp xếp (ASC, DESC) - default: DESC

## Ví dụ Request

### 1. Lấy trang đầu tiên với 10 item
```
GET /user/agents/a1b2c3d4-e5f6-7890-abcd-ef1234567890/websites?page=1&limit=10
```

### 2. <PERSON><PERSON><PERSON> kiếm website theo host
```
GET /user/agents/a1b2c3d4-e5f6-7890-abcd-ef1234567890/websites?search=example.com&page=1&limit=10
```

### 3. Lọc website đã xác minh và sắp xếp theo tên
```
GET /user/agents/a1b2c3d4-e5f6-7890-abcd-ef1234567890/websites?verify=true&sortBy=websiteName&sortDirection=ASC
```

## Response Format
```json
{
  "success": true,
  "message": "Thành công",
  "data": {
    "items": [
      {
        "id": "website-uuid-1",
        "websiteName": "Website của tôi",
        "host": "example.com",
        "verify": true,
        "isActive": true
      }
    ],
    "meta": {
      "totalItems": 25,
      "itemCount": 10,
      "itemsPerPage": 10,
      "totalPages": 3,
      "currentPage": 1,
      "hasItems": true
    }
  }
}
```

## Các Thay Đổi Đã Thực Hiện

### 1. Tạo AgentWebsiteQueryDto
- Kế thừa từ QueryDto (page, limit, search)
- Thêm sortBy, sortDirection, verify
- Validation đầy đủ với class-validator

### 2. Cập nhật UserWebsiteRepository
- Thêm method `findByAgentIdWithPagination()`
- Hỗ trợ tìm kiếm theo websiteName và host
- Lọc theo trạng thái verify
- Sắp xếp theo các trường khác nhau
- Trả về PaginatedResult với hasItems

### 3. Cập nhật AgentWebsiteService
- Method `getWebsites()` nhận thêm queryDto
- Sử dụng repository method mới
- Trả về PaginatedResult<AgentWebsiteDto>

### 4. Cập nhật AgentWebsiteController
- Nhận @Query() queryDto: AgentWebsiteQueryDto
- Sử dụng ApiResponseDto.paginated()
- Cập nhật Swagger documentation

## Lợi Ích
1. **Phân trang**: Không load tất cả data cùng lúc
2. **Tìm kiếm**: Tìm theo tên website hoặc host
3. **Lọc**: Theo trạng thái xác minh
4. **Sắp xếp**: Theo nhiều trường khác nhau
5. **hasItems**: Biết có dữ liệu hay không
6. **Performance**: Chỉ select các trường cần thiết
7. **Consistency**: Sử dụng chuẩn phân trang của hệ thống
