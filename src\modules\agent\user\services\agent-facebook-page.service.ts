import { AppException } from '@common/exceptions';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import { AgentUserRepository } from '@modules/agent/repositories';
import { INTEGRATION_ERROR_CODES } from '@modules/integration/exceptions';
import { FacebookPageRepository } from '@modules/integration/repositories';
import { Injectable, Logger } from '@nestjs/common';
import { FacebookService } from '@shared/services/facebook/facebook.service';
import { Transactional } from 'typeorm-transactional';
import {
  AgentFacebookPageDto,
  IntegrateFacebookPageDto,
  IntegrateFacebookPagesResponseDto,
  AgentFacebookPageQueryDto
} from '../dto/facebook-page';
import { PaginatedResult } from '@common/response';

/**
 * Service xử lý tích hợp Facebook Page với Agent
 */
@Injectable()
export class AgentFacebookPageService {
  private readonly logger = new Logger(AgentFacebookPageService.name);

  constructor(
    private readonly agentUserRepository: AgentUserRepository,
    private readonly facebookPageRepository: FacebookPageRepository,
    private readonly facebookService: FacebookService,
  ) { }

  /**
   * Tích hợp danh sách Facebook Page vào Agent
   * @param agentId ID của Agent
   * @param userId ID của người dùng
   * @param dto Danh sách Facebook Page cần tích hợp (UUID trong hệ thống)
   */
  @Transactional()
  async integrateFacebookPages(
    agentId: string,
    userId: number,
    dto: IntegrateFacebookPageDto,
  ): Promise<IntegrateFacebookPagesResponseDto> {
    try {
      // Kiểm tra Agent có tồn tại và thuộc về user không
      await this.checkAgentOwnership(agentId, userId);

      const results: Array<{
        facebookPageId: string;
        status: 'integrated' | 'skipped' | 'error';
        error?: string;
      }> = [];

      let integratedCount = 0;
      let skippedCount = 0;

      // Xử lý từng Facebook Page trong danh sách
      for (const facebookPageId of dto.facebookPageIds) {
        try {
          // Kiểm tra Facebook Page có tồn tại và thuộc về user không
          const facebookPage = await this.facebookPageRepository.findPageByUserIdAndPageId(
            userId,
            facebookPageId
          );

          if (!facebookPage) {
            results.push({
              facebookPageId,
              status: 'error',
              error: 'Facebook Page không tồn tại hoặc không thuộc về người dùng'
            });
            continue;
          }

          // Kiểm tra Facebook Page đã được tích hợp với agent này chưa
          if (facebookPage.agentId === agentId) {
            results.push({
              facebookPageId,
              status: 'skipped'
            });
            skippedCount++;
            continue;
          }

          // Kiểm tra Facebook Page đã được tích hợp với agent khác chưa
          if (facebookPage.agentId && facebookPage.agentId !== agentId) {
            results.push({
              facebookPageId,
              status: 'error',
              error: 'Facebook Page đã được tích hợp với agent khác'
            });
            continue;
          }

          // Đăng ký webhook cho Facebook Page
          const subscribeResult = await this.facebookService.subscribeApp(
            facebookPage.facebookPageId,
            facebookPage.pageAccessToken
          );

          if (!subscribeResult.success) {
            results.push({
              facebookPageId,
              status: 'error',
              error: 'Không thể đăng ký webhook cho Facebook Page'
            });
            continue;
          }

          // Tích hợp Facebook Page với Agent
          await this.facebookPageRepository.update(facebookPage.id, {
            agentId: agentId,
            isActive: true
          });

          results.push({
            facebookPageId,
            status: 'integrated'
          });
          integratedCount++;

        } catch (error) {
          this.logger.error(`Lỗi khi tích hợp Facebook Page ${facebookPageId}: ${error.message}`);
          results.push({
            facebookPageId,
            status: 'error',
            error: error.message || 'Lỗi không xác định'
          });
        }
      }

      return {
        message: 'Tích hợp danh sách Facebook Page thành công',
        integratedCount,
        skippedCount,
        details: results
      };

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi tích hợp danh sách Facebook Page với Agent: ${error.message}`, error.stack);
      throw new AppException(INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_INTEGRATION_FAILED);
    }
  }

  /**
   * Lấy danh sách Facebook Page trong Agent với phân trang
   * @param agentId ID của Agent
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn và phân trang
   * @returns Danh sách Facebook Page với phân trang
   */
  async getFacebookPages(
    agentId: string,
    userId: number,
    queryDto: AgentFacebookPageQueryDto
  ): Promise<PaginatedResult<AgentFacebookPageDto>> {
    try {
      // Kiểm tra Agent có tồn tại và thuộc về user không
      await this.checkAgentOwnership(agentId, userId);

      const { page, limit, search, sortBy, sortDirection } = queryDto;

      // Lấy danh sách Facebook Page đã tích hợp với Agent với phân trang
      const result = await this.facebookPageRepository.findByAgentIdWithPagination(
        agentId,
        page,
        limit,
        search,
        sortBy,
        sortDirection
      );

      // Chuyển đổi sang DTO
      const facebookPageDtos: AgentFacebookPageDto[] = result.items.map(page => ({
        id: page.id, // UUID trong hệ thống
        avatarPage: page.avatarPage,
        pageName: page.pageName,
        isActive: page.isActive
      }));

      this.logger.log(`Lấy danh sách Facebook Page thành công cho agent ${agentId}: ${facebookPageDtos.length} pages`);

      return {
        items: facebookPageDtos,
        meta: result.meta
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi lấy danh sách Facebook Page cho agent ${agentId}: ${error.message}`, error.stack);
      throw new AppException(INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_LIST_FAILED);
    }
  }

  /**
   * Gỡ Facebook Page khỏi Agent
   * @param agentId ID của Agent
   * @param pageId UUID của Facebook Page trong hệ thống
   * @param userId ID của người dùng
   */
  @Transactional()
  async removeFacebookPage(
    agentId: string,
    pageId: string,
    userId: number,
  ): Promise<void> {
    try {
      // Kiểm tra Agent có tồn tại và thuộc về user không
      await this.checkAgentOwnership(agentId, userId);

      // Kiểm tra Facebook Page có tồn tại và đã tích hợp với Agent không (sử dụng UUID)
      const facebookPage = await this.facebookPageRepository.findPageByUserIdAndPageId(userId, pageId);

      if (!facebookPage || facebookPage.agentId !== agentId) {
        throw new AppException(INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_NOT_INTEGRATED);
      }

      // Hủy đăng ký webhook
      const unsubscribeResult = await this.facebookService.unsubscribeApp(
        facebookPage.facebookPageId,
        facebookPage.pageAccessToken
      );

      if (!unsubscribeResult.success) {
        this.logger.warn(`Không thể hủy đăng ký webhook cho Facebook Page ${pageId}`);
      }

      // Gỡ bỏ agentId khỏi Facebook Page
      await this.facebookPageRepository.update(facebookPage.id, {
        agentId: null,
        isActive: false
      });
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi gỡ Facebook Page khỏi Agent: ${error.message}`, error.stack);
      throw new AppException(INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_REMOVE_FAILED);
    }
  }

  /**
   * Kiểm tra Agent có tồn tại và thuộc về user không
   * @param agentId ID của Agent
   * @param userId ID của người dùng
   */
  private async checkAgentOwnership(agentId: string, userId: number): Promise<void> {
    const agentUser = await this.agentUserRepository.findAgentByIdAndUserId(agentId, userId);

    if (!agentUser) {
      this.logger.warn(`Agent ${agentId} không tồn tại hoặc không thuộc về user ${userId}`);
      throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
    }

    this.logger.log(`Agent ${agentId} thuộc về user ${userId} - validation thành công`);
  }
}
