import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { BaseProductDto } from '../base-product/base-product.dto';
import { TicketTypeDto } from '../../advanced-info/event-advanced-info.dto';
import { HasPriceDto, StringPriceDto } from '../../price.dto';
import { ClassificationPriceDto, ClassificationStringPriceDto, CreateClassificationDto } from '../../classification.dto';
import { PriceTypeEnum } from '@modules/business/enums';

// Enum cho hình thức sự kiện
export enum EventFormatEnum {
  ONLINE = 'ONLINE',
  OFFLINE = 'OFFLINE',
  HYBRID = 'HYBRID',
}

/**
 * DTO cho việc tạo sản phẩm sự kiện (EVENT)
 * <PERSON><PERSON> thừa từ BaseProductDto và thêm các trường đặc thù cho sự kiện
 */
export class EventProductCreateDto extends BaseProductDto {
  @ApiProperty({
    description: 'Hình thức sự kiện',
    example: EventFormatEnum.ONLINE,
    enum: EventFormatEnum,
  })
  @IsEnum(EventFormatEnum)
  @IsNotEmpty()
  eventFormat: EventFormatEnum;

  @ApiProperty({
    description: 'Link tham gia sự kiện',
    example: 'https://zoom.us/j/123456789',
    required: false,
  })
  @IsOptional()
  @IsString()
  eventLink?: string;

  @ApiProperty({
    description: 'Địa điểm sự kiện',
    example: 'Trung tâm Hội nghị Quốc gia, Hà Nội',
    required: false,
  })
  @IsOptional()
  @IsString()
  eventLocation?: string;

  @ApiProperty({
    description: 'Thời gian bắt đầu (timestamp)',
    example: 1704067200000,
  })
  @IsNumber()
  startDate: number;

  @ApiProperty({
    description: 'Thời gian kết thúc (timestamp)',
    example: 1704153600000,
  })
  @IsNumber()
  endDate: number;

  @ApiProperty({
    description: 'Múi giờ',
    example: 'Asia/Ho_Chi_Minh',
  })
  @IsString()
  @IsNotEmpty()
  timezone: string;

  @ApiProperty({
    description: 'Danh sách loại vé',
    type: [TicketTypeDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TicketTypeDto)
  ticketTypes: TicketTypeDto[];

  @ApiProperty({
    description: 'Danh sách phân loại sản phẩm',
    type: [CreateClassificationDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateClassificationDto)
  classifications?: CreateClassificationDto[];

  @ApiProperty({
    description: 'Loại giá sản phẩm',
    enum: PriceTypeEnum,
    example: PriceTypeEnum.HAS_PRICE,
  })
  @IsEnum(PriceTypeEnum)
  @IsNotEmpty()
  typePrice: PriceTypeEnum;

  // Override price để có thể null cho EVENT (giá lấy từ ticket types)
  @ApiProperty({
    description: 'Thông tin giá sản phẩm - Có thể null đối với sản phẩm sự kiện (giá lấy từ ticket types)',
    oneOf: [
      { $ref: '#/components/schemas/HasPriceDto' },
      { $ref: '#/components/schemas/StringPriceDto' },
      { $ref: '#/components/schemas/ClassificationPriceDto' },
      { $ref: '#/components/schemas/ClassificationStringPriceDto' },
      { type: 'null' }
    ],
    required: false,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => Object)
  price?: HasPriceDto | StringPriceDto | ClassificationPriceDto | ClassificationStringPriceDto | null;
}
