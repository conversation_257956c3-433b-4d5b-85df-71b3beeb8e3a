import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { SmsProviderConfig } from '../interfaces/sms-provider-config.interface';

/**
 * Entity đại diện cho bảng sms_server_configurations trong cơ sở dữ liệu
 * Lưu trữ thông tin cấu hình cho máy chủ SMS
 */
@Entity('sms_server_configurations')
export class SmsServerConfiguration {
  /**
   * ID tự động tăng
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * ID người dùng
   */
  @Column({ name: 'user_id' })
  userId: number;

  /**
   * Tên nhà cung cấp SMS, ví dụ: "Twilio", "Vonage", "Nexmo", …
   */
  @Column({ name: 'provider_name', type: 'varchar', length: 100 })
  providerName: string;

  /**
   * <PERSON><PERSON><PERSON> (key) hoặc token của nhà cung cấp
   */
  @Column({ name: 'api_key', type: 'varchar', length: 255, nullable: true })
  apiKey: string | null;

  /**
   * URL endpoint để gọi API (có thể NULL nếu nhà cung cấp đã cố định)
   */
  @Column({ name: 'endpoint', type: 'varchar', length: 255, nullable: true })
  endpoint: string | null;

  /**
   * Lưu các cấu hình tùy biến, ví dụ: "Message Service SID", "Short Code", "Alphanumeric Sender ID", v.v.
   */
  @Column({ name: 'additional_settings', type: 'jsonb', nullable: true })
  additionalSettings: SmsProviderConfig | null;

  /**
   * Thời gian tạo
   */
  @Column({ name: 'created_at', type: 'bigint' })
  createdAt: number;

  /**
   * Thời gian cập nhật
   */
  @Column({ name: 'updated_at', type: 'bigint' })
  updatedAt: number;
}
