import { ApiProperty } from "@nestjs/swagger";
import { PhysicalProductCreateDto } from "../create/physical-product.dto";
import { IsNotEmpty, ValidateNested } from "class-validator";
import { Type } from "class-transformer";

/**
 * DTO cho batch tạo nhiều sản phẩm vật lý
 */
export class PhysicalProductBatchCreateDto {
  @ApiProperty({
    description: 'Danh sách sản phẩm vật lý cần tạo',
    type: [PhysicalProductCreateDto],
    example: [
      {
        name: 'Áo thun nam',
        productType: 'PHYSICAL',
        typePrice: 'HAS_PRICE',
        price: {
          listPrice: 300000,
          salePrice: 250000,
          currency: 'VND'
        },
        description: 'Áo thun nam chất liệu cotton cao cấp',
        imagesMediaTypes: ['image/jpeg'],
        tags: ['thời trang', 'nam'],
        shipmentConfig: {
          widthCm: 20,
          heightCm: 5,
          lengthCm: 25,
          weightGram: 150
        },
        inventory: {
          quantity: 100,
          minQuantity: 10,
          maxQuantity: 1000,
          unit: 'cái'
        }
      }
    ]
  })
  @IsNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => PhysicalProductCreateDto)
  products: PhysicalProductCreateDto[];
}
