import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { Integration } from '../entities';
import { PaginatedResult } from '@common/response';

/**
 * Repository xử lý truy vấn dữ liệu cho entity Integration
 */
@Injectable()
export class IntegrationRepository extends Repository<Integration> {
  private readonly logger = new Logger(IntegrationRepository.name);

  constructor(private dataSource: DataSource) {
    super(Integration, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho integration
   * @returns SelectQueryBuilder<Integration>
   */
  private createBaseQuery(): SelectQueryBuilder<Integration> {
    return this.createQueryBuilder('integration');
  }

  /**
   * Tìm tích hợp theo ID
   * @param id ID của tích hợp
   * @param userId ID của người dùng
   * @returns Tích hợp hoặc null nếu không tìm thấy
   */
  async findById(id: number, userId: number): Promise<Integration | null> {
    try {
      return await this.createBaseQuery()
        .where('integration.id = :id', { id })
        .andWhere('integration.user_id = :userId', { userId })
        .getOne();
    } catch (error) {
      this.logger.error(`Lỗi khi tìm tích hợp theo ID ${id}: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi tìm tích hợp theo ID ${id}: ${error.message}`);
    }
  }

  /**
   * Tìm danh sách tích hợp với phân trang
   * @param queryParams Tham số truy vấn
   * @returns Danh sách tích hợp với phân trang
   */
  async findAll(queryParams: any): Promise<PaginatedResult<Integration>> {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        type,
        userId,
        ownedType,
        sortBy = 'created_at',
        sortDirection = 'DESC',
      } = queryParams;

      const offset = (page - 1) * limit;
      
      const queryBuilder = this.createBaseQuery();

      // Nếu có userId thì filter theo userId (cho user), nếu không thì lấy tất cả (cho admin)
      if (userId) {
        queryBuilder.where('integration.user_id = :userId', { userId });
      }

      // Tìm kiếm theo tên tích hợp nếu có
      if (search) {
        queryBuilder.andWhere('integration.integration_name ILIKE :search', { search: `%${search}%` });
      }

      // Lọc theo loại tích hợp nếu có
      if (type) {
        queryBuilder.andWhere('integration.type = :type', { type });
      }

      // Lọc theo loại chủ sở hữu nếu có
      if (ownedType) {
        queryBuilder.andWhere('integration.owned_type = :ownedType', { ownedType });
      }



      // Đếm tổng số bản ghi
      const total = await queryBuilder.getCount();

      // Thêm sắp xếp và phân trang
      queryBuilder
        .orderBy(`integration.${sortBy}`, sortDirection as 'ASC' | 'DESC')
        .offset(offset)
        .limit(limit);

      // Lấy danh sách tích hợp
      const items = await queryBuilder.getMany();

      return {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(total / limit),
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi khi tìm danh sách tích hợp: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi tìm danh sách tích hợp: ${error.message}`);
    }
  }
} 