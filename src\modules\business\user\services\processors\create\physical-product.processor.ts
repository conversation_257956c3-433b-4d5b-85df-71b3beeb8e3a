import { Injectable, Logger } from '@nestjs/common';
import {
  InventoryRepository,
  PhysicalWarehouseRepository,
} from '@modules/business/repositories';
import { Inventory } from '@modules/business/entities';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { PriceTypeEnum } from '@modules/business/enums';
import { InventoryInfo, InventoryInfoUtils } from '@modules/business/types';
import { ValidationHelper } from '../../../helpers/validation.helper';
import { ClassificationService } from '../../classification.service';
import { CreateProductProcessor } from './create-product.processor';
import { CreateProductResult } from './create-product-orchestrator';
import { PhysicalProductCreateDto } from '../../../dto/request/create/physical-product.dto';
import { ProductInventoryDto } from '../../../dto/product-inventory.dto';

/**
 * <PERSON>or chuyên xử lý logic tạo sản phẩm vật lý
 * <PERSON>óc tách từ UserProductService để tối ưu hóa và dễ maintain
 */
@Injectable()
export class PhysicalProductProcessor {
  private readonly logger = new Logger(PhysicalProductProcessor.name);

  constructor(
    private readonly createProcessor: CreateProductProcessor,
    private readonly inventoryRepository: InventoryRepository,
    private readonly physicalWarehouseRepository: PhysicalWarehouseRepository,
    private readonly validationHelper: ValidationHelper,
    private readonly classificationService: ClassificationService,
  ) {}

  /**
   * Tạo sản phẩm vật lý hoàn chỉnh
   */
  async createPhysicalProduct(
    dto: PhysicalProductCreateDto,
    userId: number,
  ): Promise<CreateProductResult> {
    this.logger.log(`Creating PHYSICAL product: ${dto.name}`);

    // BƯỚC 1: Validate dữ liệu đầu vào cho sản phẩm vật lý
    await this.validatePhysicalProductData(dto);

    // BƯỚC 2: Xử lý custom fields và tạo metadata
    const { customFields, metadata } = await this.createProcessor.processCustomFields(dto);

    // BƯỚC 3: Tạo entity sản phẩm cơ bản
    const product = await this.createProcessor.createBaseProduct(dto, userId, metadata);

    // BƯỚC 4: Lưu sản phẩm vào database để có ID
    const savedProduct = await this.createProcessor.saveProduct(product);

    // BƯỚC 5: Xử lý hình ảnh sản phẩm
    const { imageEntries, imagesUploadUrls } = await this.createProcessor.processProductImages(dto, Date.now());

    // BƯỚC 6: Cập nhật sản phẩm với thông tin hình ảnh
    savedProduct.images = imageEntries;
    await this.createProcessor.saveProduct(savedProduct);

    // BƯỚC 7: Xử lý inventory (kho hàng)
    const inventories = await this.processPhysicalInventory(savedProduct.id, dto.inventory, userId);

    // BƯỚC 8: Xử lý classifications (phân loại sản phẩm)
    const classifications = await this.processClassifications(savedProduct.id, dto.classifications || [], userId);

    // BƯỚC 9: Lấy sản phẩm cuối cùng sau khi đã xử lý xong
    const finalProduct = await this.createProcessor.getProductById(savedProduct.id);

    // BƯỚC 10: Tạo object chứa upload URLs cho frontend
    const uploadUrls = imagesUploadUrls.length > 0 ? {
      productId: finalProduct.id.toString(),
      imagesUploadUrls: imagesUploadUrls
    } : null;

    // Trả về kết quả cuối cùng
    return {
      product: finalProduct,
      uploadUrls,
      additionalInfo: {
        inventory: inventories, // Đổi tên để tương thích với interface
        classifications
      }
    };
  }

  /**
   * Validate dữ liệu đầu vào cho sản phẩm vật lý
   */
  private async validatePhysicalProductData(dto: PhysicalProductCreateDto): Promise<void> {
    // Kiểm tra inventory có tồn tại không (bắt buộc cho sản phẩm vật lý)
    if (!dto.inventory || !Array.isArray(dto.inventory) || dto.inventory.length === 0) {
      throw new AppException(
        BUSINESS_ERROR_CODES.INVENTORY_VALIDATION_FAILED,
        'At least one inventory is required for physical products',
      );
    }

    // Kiểm tra từng inventory trong mảng
    for (const inventory of dto.inventory) {
      if (!inventory.warehouseId && !inventory.inventoryId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVENTORY_VALIDATION_FAILED,
          'Each inventory must have either warehouseId or inventoryId',
        );
      }
    }

    // Kiểm tra price có tồn tại không (bắt buộc cho sản phẩm vật lý)
    if (!dto.price) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        'Price is required for physical products',
      );
    }

    // Validate giá sản phẩm theo business rules
    // Note: PhysicalProductCreateDto không có typePrice field, giả định HAS_PRICE cho sản phẩm vật lý
    this.validationHelper.validateProductPrice(dto.price, PriceTypeEnum.HAS_PRICE, dto.productType);

    this.logger.log(`Validated physical product data for: ${dto.name}`);
  }

  /**
   * Xử lý inventory cho sản phẩm vật lý (hỗ trợ nhiều kho)
   */
  private async processPhysicalInventory(
    productId: number,
    inventoryDtos: any[],
    userId: number,
  ): Promise<any[]> {
    if (!inventoryDtos || inventoryDtos.length === 0) {
      return [];
    }

    this.logger.log(`Processing ${inventoryDtos.length} inventories for physical product: ${productId}`);

    // Tạo các bản ghi inventory cho sản phẩm vật lý ở nhiều kho
    const inventories = await Promise.all(
      inventoryDtos.map(inventoryDto =>
        this.createInventoryForPhysicalProduct(productId, inventoryDto, userId)
      )
    );

    return inventories;
  }

  /**
   * Tạo bản ghi inventory cho sản phẩm vật lý
   */
  private async createInventoryForPhysicalProduct(
    productId: number,
    inventoryDto: ProductInventoryDto,
    userId: number,
  ): Promise<Inventory> {
    this.logger.log(`Creating inventory for product ${productId} at warehouse ${inventoryDto.warehouseId}`);

    try {
      // Kiểm tra nếu sử dụng inventory có sẵn
      if (inventoryDto.inventoryId) {
        return await this.useExistingInventory(productId, inventoryDto.inventoryId, userId);
      }

      // Tạo inventory mới
      const newInventory = new Inventory();
      newInventory.productId = productId;
      newInventory.warehouseId = inventoryDto.warehouseId || null;
      newInventory.sku = inventoryDto.sku || null;
      newInventory.barcode = inventoryDto.barcode || null;

      // Thiết lập số lượng
      const availableQuantity = inventoryDto.availableQuantity || 0;
      newInventory.availableQuantity = availableQuantity;
      newInventory.currentQuantity = availableQuantity;
      newInventory.totalQuantity = availableQuantity;
      newInventory.reservedQuantity = 0;
      newInventory.defectiveQuantity = 0;
      newInventory.lastUpdated = Date.now();

      // Tạo thông tin chi tiết trong field info
      const inventoryInfo: InventoryInfo = InventoryInfoUtils.createInitialInfo({
        initialBatch: {
          sku: inventoryDto.sku || null,
          barcode: inventoryDto.barcode || null,
          currentQuantity: availableQuantity,
          totalQuantity: availableQuantity,
          availableQuantity: availableQuantity,
          reservedQuantity: 0,
          defectiveQuantity: 0,
          status: 'ACTIVE',
          notes: `Initial inventory for product ${productId}`,
        },
        metadata: {
          notes: `Created for product ${productId} at warehouse ${inventoryDto.warehouseId}`,
          status: 'ACTIVE',
        },
      });

      newInventory.info = inventoryInfo;

      // Lưu vào database
      const savedInventory = await this.inventoryRepository.save(newInventory);

      this.logger.log(`Successfully created inventory ${savedInventory.id} for product ${productId}`);
      return savedInventory;

    } catch (error) {
      this.logger.error(`Error creating inventory for product ${productId}: ${error.message}`, error.stack);
      throw new AppException(
        BUSINESS_ERROR_CODES.INVENTORY_CREATION_FAILED,
        `Failed to create inventory for product ${productId}: ${error.message}`,
      );
    }
  }

  /**
   * Sử dụng inventory có sẵn
   */
  private async useExistingInventory(
    productId: number,
    inventoryId: number,
    userId: number,
  ): Promise<Inventory> {
    this.logger.log(`Using existing inventory ${inventoryId} for product ${productId}`);

    try {
      // Tìm inventory có sẵn
      const existingInventory = await this.inventoryRepository.findOne({
        where: { id: inventoryId }
      });

      if (!existingInventory) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVENTORY_NOT_FOUND,
          `Inventory with ID ${inventoryId} not found`,
        );
      }

      // Kiểm tra inventory đã được sử dụng cho sản phẩm khác chưa
      if (existingInventory.productId && existingInventory.productId !== productId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVENTORY_ALREADY_USED,
          `Inventory ${inventoryId} is already used for another product`,
        );
      }

      // Cập nhật productId nếu chưa có
      if (!existingInventory.productId) {
        existingInventory.productId = productId;
        existingInventory.lastUpdated = Date.now();
        await this.inventoryRepository.save(existingInventory);
      }

      this.logger.log(`Successfully linked existing inventory ${inventoryId} to product ${productId}`);
      return existingInventory;

    } catch (error) {
      this.logger.error(`Error using existing inventory ${inventoryId}: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.INVENTORY_VALIDATION_FAILED,
        `Failed to use existing inventory ${inventoryId}: ${error.message}`,
      );
    }
  }

  /**
   * Xử lý classifications cho sản phẩm
   */
  private async processClassifications(
    productId: number,
    classificationsDto: any[],
    userId: number,
  ): Promise<any[]> {
    if (!classificationsDto || classificationsDto.length === 0) {
      return [];
    }

    this.logger.log(`Processing ${classificationsDto.length} classifications for product: ${productId}`);

    // TODO: Implement logic từ service gốc
    // Tạo các bản ghi classification nếu có
    // const classifications = await this.createClassificationsForProduct(productId, classificationsDto, userId);

    // Placeholder return
    return classificationsDto.map((classification, index) => ({
      id: index + 1,
      productId,
      name: classification.name || `Classification ${index + 1}`,
      // ... other classification fields
    }));
  }
}
