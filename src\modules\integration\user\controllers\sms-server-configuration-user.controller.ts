import {
  Controller,
  UseGuards,
  Post,
  Get,
  Put,
  Delete,
  Body,
  Param,
  ParseIntPipe
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBody
} from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@modules/auth/decorators';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { SmsServerConfigurationUserService } from '../services';
import { SWAGGER_API_TAGS } from '@/common/swagger/swagger.tags';
import { ApiResponseDto } from '@/common/response';
import {
  CreateSmsServerDto,
  UpdateSmsServerDto,
  SmsServerResponseDto
} from '../dto/sms';


@ApiTags(SWAGGER_API_TAGS.INTEGRATION_SMS)
@Controller('user/integration/sms-server')
@UseGuards(JwtUserGuard)
@ApiBearerAuth("JWT-auth")
export class SmsServerConfigurationUserController {
  constructor(
    private readonly smsServerConfigurationUserService: SmsServerConfigurationUserService,
  ) {}

  /**
   * Lấy danh sách cấu hình SMS
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách cấu hình máy chủ SMS' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách cấu hình máy chủ SMS',
    type: [SmsServerResponseDto]
  })
  async findAll(@CurrentUser() user: JwtPayload): Promise<ApiResponseDto<SmsServerResponseDto[]>> {
    const configs = await this.smsServerConfigurationUserService.findAll(user.id);
    return ApiResponseDto.success(configs, 'Lấy danh sách cấu hình SMS thành công');
  }

  /**
   * Lấy cấu hình SMS theo ID
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy cấu hình máy chủ SMS theo ID' })
  @ApiParam({ name: 'id', description: 'ID của cấu hình SMS', type: Number })
  @ApiResponse({
    status: 200,
    description: 'Thông tin cấu hình máy chủ SMS',
    type: SmsServerResponseDto
  })
  async findOne(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: JwtPayload
  ): Promise<ApiResponseDto<SmsServerResponseDto>> {
    const config = await this.smsServerConfigurationUserService.findOne(id, user.id);
    return ApiResponseDto.success(config, 'Lấy cấu hình SMS thành công');
  }

  /**
   * Tạo mới cấu hình SMS tổng quát
   */
  @Post()
  @ApiOperation({ summary: 'Tạo mới cấu hình máy chủ SMS' })
  @ApiBody({ type: CreateSmsServerDto })
  @ApiResponse({
    status: 201,
    description: 'Cấu hình máy chủ SMS đã được tạo',
    type: SmsServerResponseDto
  })
  async create(
    @Body() createDto: CreateSmsServerDto,
    @CurrentUser() user: JwtPayload
  ): Promise<ApiResponseDto<SmsServerResponseDto>> {
    const config = await this.smsServerConfigurationUserService.create(createDto, user.id);
    return ApiResponseDto.created(config, 'Tạo cấu hình SMS thành công');
  }



  /**
   * Cập nhật cấu hình SMS
   */
  @Put(':id')
  @ApiOperation({ summary: 'Cập nhật cấu hình máy chủ SMS' })
  @ApiParam({ name: 'id', description: 'ID của cấu hình SMS', type: Number })
  @ApiBody({ type: UpdateSmsServerDto })
  @ApiResponse({
    status: 200,
    description: 'Cấu hình máy chủ SMS đã được cập nhật',
    type: SmsServerResponseDto
  })
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateSmsServerDto,
    @CurrentUser() user: JwtPayload
  ): Promise<ApiResponseDto<SmsServerResponseDto>> {
    const config = await this.smsServerConfigurationUserService.update(id, updateDto, user.id);
    return ApiResponseDto.success(config, 'Cập nhật cấu hình SMS thành công');
  }



  /**
   * Xóa cấu hình SMS
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa cấu hình máy chủ SMS' })
  @ApiParam({ name: 'id', description: 'ID của cấu hình SMS', type: Number })
  @ApiResponse({
    status: 200,
    description: 'Cấu hình máy chủ SMS đã được xóa'
  })
  async remove(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: JwtPayload
  ): Promise<ApiResponseDto<null>> {
    await this.smsServerConfigurationUserService.remove(id, user.id);
    return ApiResponseDto.success(null, 'Xóa cấu hình SMS thành công');
  }


}
