import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { QueryDto } from '@common/dto';
import { OwnedTypeEnum } from '@modules/integration/enums';

/**
 * DTO cho việc truy vấn danh sách tích hợp bởi admin
 */
export class QueryIntegrationAdminDto extends QueryDto {
  /**
   * Tìm kiếm theo tên tích hợp
   * @example "Google"
   */
  @ApiPropertyOptional({
    description: 'Tìm kiếm theo tên tích hợp',
    example: 'Google'
  })
  @IsOptional()
  @IsString()
  declare search?: string;

  /**
   * Lọc theo loại tích hợp
   * @example "ANALYTICS"
   */
  @ApiPropertyOptional({
    description: 'Lọc theo loại tích hợp',
    example: 'ANALYTICS'
  })
  @IsOptional()
  @IsString()
  type?: string;



  /**
   * Lọc theo loại chủ sở hữu tích hợp
   * @example "USER"
   */
  @ApiPropertyOptional({
    description: 'Lọc theo loại chủ sở hữu tích hợp',
    enum: OwnedTypeEnum,
    example: OwnedTypeEnum.USER
  })
  @IsOptional()
  @IsEnum(OwnedTypeEnum, { message: 'Loại chủ sở hữu không hợp lệ' })
  ownedType?: OwnedTypeEnum;
}
