import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  Min,
  ValidateNested,
} from 'class-validator';
import { BaseProductDto } from '../base-product/base-product.dto';
import { ComboInfoItemDto } from '../../advanced-info/combo-advanced-info.dto';
import { HasPriceDto, StringPriceDto } from '../../price.dto';
import { ClassificationPriceDto, ClassificationStringPriceDto, CreateClassificationDto } from '../../classification.dto';
import { PriceTypeEnum } from '@modules/business/enums';

/**
 * DTO cho việc tạo sản phẩm combo (COMBO)
 * Kế thừa từ BaseProductDto và thêm các trường đặc thù cho combo
 */
export class ComboProductCreateDto extends BaseProductDto {
  @ApiProperty({
    description: 'Số lượt mua',
    example: 0,
  })
  @IsNumber()
  @Min(0)
  purchaseCount: number;

  @ApiProperty({
    description: 'Thông tin các sản phẩm trong combo',
    type: [ComboInfoItemDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ComboInfoItemDto)
  comboItems: ComboInfoItemDto[];

  @ApiProperty({
    description: 'Danh sách phân loại sản phẩm',
    type: [CreateClassificationDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateClassificationDto)
  classifications?: CreateClassificationDto[];

  @ApiProperty({
    description: 'Loại giá sản phẩm',
    enum: PriceTypeEnum,
    example: PriceTypeEnum.HAS_PRICE,
  })
  @IsEnum(PriceTypeEnum)
  @IsNotEmpty()
  typePrice: PriceTypeEnum;

  // Override price để bắt buộc cho COMBO
  @ApiProperty({
    description: 'Thông tin giá sản phẩm - Bắt buộc đối với sản phẩm combo',
    oneOf: [
      { $ref: '#/components/schemas/HasPriceDto' },
      { $ref: '#/components/schemas/StringPriceDto' },
      { $ref: '#/components/schemas/ClassificationPriceDto' },
      { $ref: '#/components/schemas/ClassificationStringPriceDto' }
    ],
  })
  @IsNotEmpty()
  @IsObject()
  @ValidateNested()
  @Type(() => Object)
  price: HasPriceDto | StringPriceDto | ClassificationPriceDto | ClassificationStringPriceDto;
}
